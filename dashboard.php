<?php
session_start();
include 'config.php';

// Check if user is logged in
if(!isset($_SESSION['user_id'])) {
    header("Location: login.php");
    exit();
}

// Get user information
$user_id = $_SESSION['user_id'];
$username = $_SESSION['username'];

// Get current user's role
$check_admin = "SELECT role, fullname FROM users WHERE id = ?";
$stmt = $conn->prepare($check_admin);
$stmt->bind_param("i", $user_id);
$stmt->execute();
$result = $stmt->get_result();
$current_user = $result->fetch_assoc();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard</title>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="uxintace-topbar.css">
    <style>
        body {
            font-family: 'Poppins', sans-serif;
            background: linear-gradient(135deg, #e8f5e9 0%, #c8e6c9 100%);
            margin: 0;
            padding: 0;
            min-height: 100vh;
        }

        /* Topbar Styles - Enhanced with animations */
        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes slideInRight {
            from {
                opacity: 0;
                transform: translateX(-20px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        @keyframes pulse {
            0% {
                box-shadow: 0 0 0 0 rgba(46, 125, 50, 0.4);
            }
            70% {
                box-shadow: 0 0 0 10px rgba(46, 125, 50, 0);
            }
            100% {
                box-shadow: 0 0 0 0 rgba(46, 125, 50, 0);
            }
        }

        @keyframes ripple {
            0% {
                transform: scale(0);
                opacity: 0.5;
            }
            100% {
                transform: scale(2);
                opacity: 0;
            }
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% {
                transform: translateY(0);
            }
            40% {
                transform: translateY(-5px);
            }
            60% {
                transform: translateY(-2px);
            }
        }

        /* Ripple effect for buttons */
        .ripple-effect {
            position: absolute;
            border-radius: 50%;
            background-color: rgba(255, 255, 255, 0.4);
            transform: scale(0);
            animation: ripple 0.6s linear;
            pointer-events: none;
        }

        /* Animation for icons */
        .nav-item i.animated {
            animation: bounce 0.5s ease;
        }

        /* Position relative for ripple effect */
        .nav-item, .user-profile span, .logout-btn {
            position: relative;
            overflow: hidden;
        }

        .topbar {
            background: #2e7d32;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 20px;
            height: 50px;
        }

        .nav-menu {
            display: flex;
            align-items: center;
        }

        .nav-item {
            background: rgba(255, 255, 255, 0.15);
            color: white;
            text-decoration: none;
            padding: 8px 15px;
            border-radius: 4px;
            margin-right: 10px;
            font-size: 14px;
            display: flex;
            align-items: center;
            transition: all 0.3s ease;
        }

        .nav-item:hover {
            background: rgba(255, 255, 255, 0.25);
        }

        .nav-item.active {
            background: rgba(255, 255, 255, 0.3);
            font-weight: 500;
        }

        .nav-item i {
            margin-right: 8px;
        }

        .user-profile {
            background: rgba(255, 255, 255, 0.15);
            color: white;
            padding: 8px 15px;
            border-radius: 4px;
            font-size: 14px;
            display: flex;
            align-items: center;
        }

        .user-profile i {
            margin-right: 8px;
        }

        /* Secondary Navigation Bar */
        .secondary-nav {
            background: #f5f5f5;
            position: fixed;
            top: 50px;
            left: 0;
            right: 0;
            z-index: 999;
            display: flex;
            padding: 5px 20px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }

        .secondary-nav-btn {
            background: #e8f5e9;
            color: #2e7d32;
            text-decoration: none;
            padding: 6px 15px;
            border-radius: 4px;
            margin-right: 10px;
            font-size: 14px;
            display: flex;
            align-items: center;
            transition: all 0.3s ease;
            border: 1px solid #c8e6c9;
        }

        .secondary-nav-btn:hover {
            background: #c8e6c9;
        }

        .secondary-nav-btn i {
            margin-right: 8px;
            color: #2e7d32;
        }

        .badge {
            background: #f44336;
            color: white;
            border-radius: 50%;
            padding: 2px 6px;
            font-size: 11px;
            margin-left: 5px;
        }

        /* Main Content Styles */
        .main-content {
            margin-top: 60px;
            padding: 20px;
            width: 100%;
            box-sizing: border-box;
        }

        .header {
            background: transparent;
            padding: 0 0 15px 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        }

        .user-profile {
            display: flex;
            align-items: center;
            gap: 12px;
            animation: fadeInDown 0.5s ease-out forwards;
            animation-delay: 0.3s;
            opacity: 0;
        }

        .user-profile span {
            padding: 8px 16px;
            background: rgba(255, 255, 255, 0.15);
            border-radius: 30px;
            color: white;
            font-size: 13px;
            font-weight: 500;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            border: 1px solid rgba(255, 255, 255, 0.2);
            display: flex;
            align-items: center;
        }

        .user-profile span:before {
            content: '\f007';
            font-family: 'Font Awesome 5 Free';
            font-weight: 900;
            margin-right: 8px;
            font-size: 14px;
        }

        .user-profile span:hover {
            background: rgba(255, 255, 255, 0.25);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            animation: pulse 1.5s infinite;
        }

        .logout-btn {
            padding: 8px 16px;
            background: rgba(220, 53, 69, 0.1);
            border-radius: 30px;
            color: white;
            text-decoration: none;
            font-size: 13px;
            font-weight: 500;
            transition: all 0.3s ease;
            border: 1px solid rgba(220, 53, 69, 0.2);
            display: flex;
            align-items: center;
        }

        .logout-btn:before {
            content: '\f2f5';
            font-family: 'Font Awesome 5 Free';
            font-weight: 900;
            margin-right: 8px;
            font-size: 14px;
        }

        .logout-btn:hover {
            background: rgba(220, 53, 69, 0.8);
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(220, 53, 69, 0.3);
        }

        /* Welcome Banner */
        .welcome-banner {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin: 20px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
            border: 1px solid rgba(0, 0, 0, 0.05);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .banner-text h1 {
            color: #2e7d32;
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 5px;
        }

        .banner-text p {
            color: #666;
            font-size: 14px;
        }

        .banner-buttons {
            display: flex;
            gap: 10px;
        }

        .banner-btn {
            padding: 8px 16px;
            border-radius: 4px;
            text-decoration: none;
            font-size: 13px;
            font-weight: 500;
            transition: all 0.2s ease;
        }

        .btn-primary {
            background: #2e7d32;
            color: white;
        }

        .btn-secondary {
            background: #e8f5e9;
            color: #2e7d32;
            border: 1px solid #c8e6c9;
        }

        .btn-primary:hover, .btn-secondary:hover {
            opacity: 0.9;
            transform: translateY(-1px);
        }

        /* Feature Cards */
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
            gap: 20px;
            margin: 0 20px 20px 20px;
        }

        .feature-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            transition: all 0.3s ease;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
            border: 1px solid rgba(0, 0, 0, 0.05);
        }

        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }

        .feature-icon {
            width: 50px;
            height: 50px;
            background: #47a84e;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 15px;
            color: white;
        }

        .feature-icon i {
            font-size: 20px;
        }

        .feature-title {
            color: #333;
            margin-bottom: 8px;
            font-weight: 500;
            font-size: 15px;
        }

        .feature-card p {
            color: #666;
            font-size: 13px;
        }

        /* Barangay Cards */
        .barangay-overview {
            margin: 0 20px 20px 20px;
        }

        .barangay-overview h2 {
            color: #333;
            font-size: 16px;
            font-weight: 500;
            margin-bottom: 15px;
        }

        .barangay-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            gap: 20px;
        }

        .barangay-card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
            border: 1px solid rgba(0, 0, 0, 0.05);
            transition: all 0.3s ease;
        }

        .barangay-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }

        .barangay-card h3 {
            color: #2e7d32;
            margin-bottom: 15px;
            font-size: 15px;
            font-weight: 500;
        }

        .barangay-stats {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .stat-item {
            display: flex;
            align-items: center;
            gap: 10px;
            color: #666;
            font-size: 13px;
        }

        .stat-item i {
            color: #47a84e;
            width: 16px;
            text-align: center;
            font-size: 14px;
        }

        .welcome-section {
            margin: 0 20px 20px 20px;
        }

        .welcome-section h1 {
            color: #333;
            font-size: 18px;
            font-weight: 500;
            margin-bottom: 5px;
        }

        .welcome-section p {
            color: #666;
            font-size: 14px;
        }

        @media (max-width: 768px) {
            .barangay-grid {
                grid-template-columns: 1fr;
            }

            .welcome-banner {
                flex-direction: column;
                text-align: center;
                gap: 15px;
            }

            .banner-buttons {
                width: 100%;
                justify-content: center;
            }

            .features {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- Topbar -->
    <div class="topbar">
        <div class="nav-menu">
            <a href="dashboard.php" class="nav-item active">
                <i class="fas fa-home"></i> Dashboard
            </a>
            <a href="add_patient.php" class="nav-item">
                <i class="fas fa-user-plus"></i> Add Patient
            </a>
            <a href="view_patients.php" class="nav-item">
                <i class="fas fa-users"></i> Patient Records
            </a>
            <?php if($current_user['role'] === 'super_admin' || $current_user['role'] === 'admin'): ?>
            <a href="manage_users.php" class="nav-item">
                <i class="fas fa-user-cog"></i> User Management
            </a>
            <?php endif; ?>
            <a href="logout.php" class="nav-item">
                <i class="fas fa-sign-out-alt"></i> Logout
            </a>
        </div>
        <div class="user-profile">
            <i class="fas fa-user"></i> <?php echo htmlspecialchars($username); ?>
        </div>
    </div>

    <!-- Secondary Navigation Bar -->
    <?php if($current_user['role'] === 'super_admin' || $current_user['role'] === 'admin'): ?>
    <div class="secondary-nav">
        <a href="manage_users.php#pending-section" class="secondary-nav-btn">
            <i class="fas fa-user-clock"></i> Pending Approvals
        </a>
        <a href="manage_users.php#rejected-section" class="secondary-nav-btn">
            <i class="fas fa-user-times"></i> Rejected Users
            <?php
            // Count rejected users
            $count_rejected = $conn->query("SELECT COUNT(*) as count FROM rejected_users");
            $rejected_count = $count_rejected->fetch_assoc()['count'];
            if($rejected_count > 0):
            ?>
            <span class="badge"><?php echo $rejected_count; ?></span>
            <?php endif; ?>
        </a>
    </div>
    <?php endif; ?>

    <!-- Main Content -->
    <div class="main-content" style="margin-top: <?php echo ($current_user['role'] === 'super_admin' || $current_user['role'] === 'admin') ? '95px' : '60px'; ?>">
        <!-- Welcome Banner -->
        <div class="welcome-banner">
            <div class="banner-text">
                <h1>Welcome!</h1>
                <p>Manage laboratory exams and patient records efficiently</p>
            </div>
            <div class="banner-buttons">
                <a href="register_patient.php" class="banner-btn btn-primary">Register Patient</a>
                <a href="view_patients.php" class="banner-btn btn-secondary">View Records</a>
            </div>
        </div>

        <!-- Feature Cards -->
        <div class="features">
            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-user-plus"></i>
                </div>
                <h3 class="feature-title">Patient Registration</h3>
                <p>Register new patients</p>
            </div>
            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-flask"></i>
                </div>
                <h3 class="feature-title">Laboratory Exam</h3>
                <p>Manage lab tests</p>
            </div>
            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-users"></i>
                </div>
                <h3 class="feature-title">Patient Records</h3>
                <p>View patient history</p>
            </div>
            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-chart-bar"></i>
                </div>
                <h3 class="feature-title">Reports</h3>
                <p>Generate reports</p>
            </div>
        </div>

        <div class="dashboard-content">
            <div class="welcome-section">
                <h1>Welcome, <?php echo htmlspecialchars($current_user['fullname'] ?? $username); ?>!</h1>
                <p>Here's what's happening in your barangay health center today.</p>
            </div>

            <?php if(isset($current_user['role']) && $current_user['role'] === 'super_admin'): ?>
            <div class="barangay-overview">
                <h2>Barangay Overview</h2>
                <div class="barangay-grid">
                    <?php
                    $barangays = [
                        'Baclaran', 'B. F. Homes', 'Don Bosco', 'Don Galo', 'La Huerta',
                        'Merville', 'Moonwalk', 'San Antonio', 'San Dionisio', 'San Isidro',
                        'San Martin de Porres', 'Marcelo Green', 'Sto. Niño', 'Sun Valley', 'Tambo',
                        'Vitalez'
                    ];
                    sort($barangays); // Sort array alphabetically

                    foreach($barangays as $barangay) {
                        // Get patient count for this barangay
                        $stmt = $conn->prepare("SELECT COUNT(*) as count FROM patients WHERE barangay = ?");
                        $stmt->bind_param("s", $barangay);
                        $stmt->execute();
                        $result = $stmt->get_result();
                        $patient_count = $result->fetch_assoc()['count'];

                        // Get staff count for this barangay
                        $stmt = $conn->prepare("SELECT COUNT(*) as count FROM users WHERE barangay = ? AND role = 'staff'");
                        $stmt->bind_param("s", $barangay);
                        $stmt->execute();
                        $result = $stmt->get_result();
                        $staff_count = $result->fetch_assoc()['count'];

                        // Get admin count for this barangay
                        $stmt = $conn->prepare("SELECT COUNT(*) as count FROM users WHERE barangay = ? AND role = 'admin'");
                        $stmt->bind_param("s", $barangay);
                        $stmt->execute();
                        $result = $stmt->get_result();
                        $admin_count = $result->fetch_assoc()['count'];
                    ?>
                    <div class="barangay-card">
                        <h3><?php echo htmlspecialchars($barangay); ?></h3>
                        <div class="barangay-stats">
                            <div class="stat-item">
                                <i class="fas fa-users"></i>
                                <span><?php echo $patient_count; ?> Patients</span>
                            </div>
                            <div class="stat-item">
                                <i class="fas fa-user-nurse"></i>
                                <span><?php echo $staff_count; ?> Staff</span>
                            </div>
                            <div class="stat-item">
                                <i class="fas fa-user-shield"></i>
                                <span><?php echo $admin_count; ?> Admin</span>
                            </div>
                        </div>
                    </div>
                    <?php } ?>
                </div>
            </div>
            <?php endif; ?>

            <div class="stats-grid">
                <!-- Rest of the existing code -->
            </div>
        </div>
    </div>

    <!-- JavaScript for enhanced animations and interactions -->
    <script>
        // Initialize animations for the topbar
        document.addEventListener('DOMContentLoaded', function() {
            // Set all nav items to be visible
            const navItems = document.querySelectorAll('.nav-item');
            navItems.forEach(item => {
                setTimeout(() => {
                    item.style.opacity = '1';
                }, 100);
            });

            // Set user profile to be visible
            const userProfile = document.querySelector('.user-profile');
            if (userProfile) {
                setTimeout(() => {
                    userProfile.style.opacity = '1';
                }, 300);
            }
        });
    </script>
</body>
</html>