<?php
session_start();
include 'config.php';
include 'activity_logger.php';

// Check if user is logged in
if(!isset($_SESSION['user_id'])) {
    header("Location: login.php");
    exit();
}

// Get current user information
$user_id = $_SESSION['user_id'];
$check_user = "SELECT * FROM users WHERE id = ?";
$stmt = $conn->prepare($check_user);
$stmt->bind_param("i", $user_id);
$stmt->execute();
$result = $stmt->get_result();
$current_user = $result->fetch_assoc();

// Check if user is admin or super_admin
if($current_user['role'] !== 'admin' && $current_user['role'] !== 'super_admin' && $current_user['role'] !== 'staff') {
    header("Location: dashboard.php");
    exit();
}

$username = $_SESSION['username'];

// Get patient ID from URL
if (!isset($_GET['id']) || empty($_GET['id'])) {
    $_SESSION['error'] = "No patient ID provided";
    header("Location: view_patients.php");
    exit();
}

$patient_id = (int)$_GET['id'];

// Get patient information
$sql = "SELECT * FROM patients WHERE id = ?";
$stmt = $conn->prepare($sql);
$stmt->bind_param("i", $patient_id);
$stmt->execute();
$result = $stmt->get_result();
$patient = $result->fetch_assoc();

if (!$patient) {
    $_SESSION['error'] = "Patient not found";
    header("Location: view_patients.php");
    exit();
}

// Handle form submission
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    try {
        $fullname = $conn->real_escape_string($_POST['fullname']);
        $age = $conn->real_escape_string($_POST['age']);
        $sex = $conn->real_escape_string($_POST['sex']);
        $address = $conn->real_escape_string($_POST['address']);
        $purpose = $conn->real_escape_string($_POST['purpose']);
        $registration_date = !empty($_POST['registration_date']) ? $conn->real_escape_string($_POST['registration_date']) : date('Y-m-d');
        $barangay = $conn->real_escape_string($_POST['barangay']);
        $diagnosis = isset($_POST['diagnosis']) ? $conn->real_escape_string($_POST['diagnosis']) : '';

        // First check if diagnosis column exists
        $check_diagnosis = $conn->query("SHOW COLUMNS FROM patients LIKE 'diagnosis'");

        // If diagnosis column doesn't exist, add it
        if($check_diagnosis->num_rows == 0) {
            $add_column = "ALTER TABLE patients ADD COLUMN diagnosis TEXT NULL AFTER purpose";
            $conn->query($add_column);
        }

        // Check if the registration_date column exists
        $check_column = $conn->query("SHOW COLUMNS FROM patients LIKE 'registration_date'");

        if($check_column->num_rows > 0) {
            // Use the new schema with registration_date
            $sql = "UPDATE patients SET
                    fullname = ?,
                    age = ?,
                    sex = ?,
                    address = ?,
                    purpose = ?,
                    barangay = ?,
                    registration_date = ?,
                    diagnosis = ?
                    WHERE id = ?";
            $stmt = $conn->prepare($sql);
            $stmt->bind_param("sississsi", $fullname, $age, $sex, $address, $purpose, $barangay, $registration_date, $diagnosis, $patient_id);
        } else {
            // Use the old schema without registration_date
            $sql = "UPDATE patients SET
                    fullname = ?,
                    age = ?,
                    sex = ?,
                    address = ?,
                    purpose = ?,
                    barangay = ?,
                    diagnosis = ?
                    WHERE id = ?";
            $stmt = $conn->prepare($sql);
            $stmt->bind_param("sississi", $fullname, $age, $sex, $address, $purpose, $barangay, $diagnosis, $patient_id);
        }

        if ($stmt->execute()) {
            $details = "Updated patient: " . $fullname . " (ID: " . $patient_id . ")";
            logActivity($conn, $_SESSION['user_id'], "Edit Patient", $details);
            $_SESSION['success'] = "Patient updated successfully!";
            header("Location: view_patients.php");
            exit();
        } else {
            $error_message = "Error: " . $stmt->error;
        }

        $stmt->close();
    } catch (Exception $e) {
        $error_message = "Error: " . $e->getMessage();
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Edit Patient</title>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Poppins', sans-serif;
        }

        body {
            font-family: 'Poppins', sans-serif;
            background: linear-gradient(135deg, #e8f5e9 0%, #c8e6c9 100%);
            margin: 0;
            padding: 0;
            min-height: 100vh;
        }

        /* Topbar Styles */
        .topbar {
            background: white;
            padding: 12px 20px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            display: flex;
            justify-content: space-between;
            align-items: center;
            height: 64px;
        }

        .logo-section {
            display: flex;
            align-items: center;
            height: 100%;
        }

        .logo {
            color: #2e7d32;
            font-size: 20px;
            font-weight: 600;
            display: flex;
            align-items: center;
            text-decoration: none;
            height: 100%;
            padding-right: 20px;
            margin-right: 20px;
            border-right: 1px solid #f0f0f0;
        }

        .logo-image {
            height: 40px;
            width: auto;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 3px;
        }

        .logo-text {
            display: flex;
            flex-direction: column;
        }

        .logo-title {
            color: #2e7d32;
            font-size: 18px;
            font-weight: 600;
            line-height: 1.1;
        }

        .logo-subtitle {
            color: #47a84e;
            font-size: 12px;
            font-weight: 500;
        }

        .nav-menu {
            display: flex;
            align-items: center;
            height: 100%;
        }

        .nav-item {
            display: flex;
            align-items: center;
            padding: 0 15px;
            color: #666;
            text-decoration: none;
            border-radius: 6px;
            transition: all 0.2s ease;
            font-size: 14px;
            height: 40px;
        }

        .nav-item:hover {
            background: #f0f7f0;
            color: #2e7d32;
        }

        .nav-item.active {
            background: #e8f5e9;
            color: #2e7d32;
            font-weight: 500;
        }

        .nav-item i {
            margin-right: 8px;
            width: 16px;
            text-align: center;
            font-size: 14px;
        }

        /* Main Content Styles */
        .main-content {
            margin-top: 60px;
            padding: 20px;
            width: 100%;
            box-sizing: border-box;
        }

        .user-profile {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .user-profile span {
            padding: 6px 12px;
            background: white;
            border-radius: 4px;
            color: #2e7d32;
            font-size: 13px;
            font-weight: 500;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
        }

        .logout-btn {
            padding: 6px 12px;
            background: #ffebee;
            border-radius: 4px;
            color: #dc3545;
            text-decoration: none;
            font-size: 13px;
            font-weight: 500;
            transition: all 0.2s ease;
        }

        .logout-btn:hover {
            background: #dc3545;
            color: white;
        }

        /* Form Styles */
        .form-container {
            background: white;
            border-radius: 8px;
            padding: 25px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
            max-width: 800px;
            margin: 0 auto;
        }

        .form-title {
            font-size: 18px;
            color: #333;
            margin-bottom: 20px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            margin-bottom: 6px;
            font-size: 14px;
            color: #333;
        }

        .form-control {
            width: 100%;
            padding: 10px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .form-control:focus {
            outline: none;
            border-color: #47a84e;
            box-shadow: 0 0 0 2px rgba(71, 168, 78, 0.1);
        }

        .form-text {
            font-size: 12px;
            color: #666;
            margin-top: 4px;
        }

        textarea.form-control {
            min-height: 100px;
            resize: vertical;
        }

        .btn-submit {
            background: #2e7d32;
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 4px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-submit:hover {
            background: #256e29;
        }

        .success-message {
            background: #d4edda;
            color: #155724;
            padding: 12px 15px;
            border-radius: 4px;
            margin-bottom: 20px;
            font-size: 14px;
        }

        .error-message {
            background: #f8d7da;
            color: #721c24;
            padding: 12px 15px;
            border-radius: 4px;
            margin-bottom: 20px;
            font-size: 14px;
        }

        .control-number-preview {
            background-color: #f8f9fa;
            color: #495057;
            font-family: monospace;
            letter-spacing: 1px;
        }
    </style>
</head>
<body>
    <!-- Topbar -->
    <div class="topbar">
        <div class="logo-section">

            <nav class="nav-menu">
                <a href="dashboard.php" class="nav-item">
                    <i class="fas fa-home"></i>
                    Dashboard
                </a>
                <?php if($current_user['role'] === 'super_admin' || $current_user['role'] === 'admin' || $current_user['role'] === 'staff'): ?>
                <a href="add_patient.php" class="nav-item">
                    <i class="fas fa-user-plus"></i>
                    Add Patient
                </a>
                <?php endif; ?>
                <a href="view_patients.php" class="nav-item active">
                    <i class="fas fa-users"></i>
                    Patient Records
                </a>
                <?php if($current_user['role'] !== 'staff'): ?>
                <a href="manage_users.php" class="nav-item">
                    <i class="fas fa-users-cog"></i>
                    User Management
                </a>
                <?php endif; ?>
                <a href="logout.php" class="nav-item">
                    <i class="fas fa-sign-out-alt"></i>
                    Logout
                </a>
            </nav>
        </div>
        <div class="user-profile">
            <span><?php echo htmlspecialchars($username); ?></span>
        </div>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <div class="form-container">
            <h2 class="form-title">Edit Patient Information</h2>

            <?php if(isset($success_message)): ?>
                <div class="success-message"><?php echo $success_message; ?></div>
            <?php endif; ?>

            <?php if(isset($error_message)): ?>
                <div class="error-message"><?php echo $error_message; ?></div>
            <?php endif; ?>

            <form method="POST" action="">
                <div class="form-group">
                    <label class="form-label">Control Number</label>
                    <input type="text" class="form-control control-number-preview" value="<?php echo htmlspecialchars($patient['control_number']); ?>" readonly>
                </div>

                <div class="form-group">
                    <label for="registration_date" class="form-label">Registration Date</label>
                    <input type="date" id="registration_date" name="registration_date" class="form-control" value="<?php echo isset($patient['registration_date']) ? htmlspecialchars($patient['registration_date']) : date('Y-m-d'); ?>">
                </div>

                <div class="form-group">
                    <label for="fullname" class="form-label">Full Name</label>
                    <input type="text" id="fullname" name="fullname" required placeholder="Enter patient's full name" class="form-control" pattern=".*,.*" title="Format should be: Last Name, First Name Middle Name" value="<?php echo htmlspecialchars($patient['fullname']); ?>">
                    <small class="form-text">Format: Last Name, First Name Middle Name. Example: Dela Cruz, Juan Martinez</small>
                </div>

                <div class="form-group">
                    <label for="age" class="form-label">Age</label>
                    <input type="number" id="age" name="age" min="0" max="150" required placeholder="Enter age" class="form-control" value="<?php echo htmlspecialchars($patient['age']); ?>">
                </div>

                <div class="form-group">
                    <label for="sex" class="form-label">Sex</label>
                    <select id="sex" name="sex" required class="form-control">
                        <option value="">Select Sex</option>
                        <option value="Male" <?php echo ($patient['sex'] == 'Male') ? 'selected' : ''; ?>>Male</option>
                        <option value="Female" <?php echo ($patient['sex'] == 'Female') ? 'selected' : ''; ?>>Female</option>
                        <option value="Other" <?php echo ($patient['sex'] == 'Other') ? 'selected' : ''; ?>>Other</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="address" class="form-label">Address</label>
                    <textarea id="address" name="address" required placeholder="Enter complete address" class="form-control"><?php echo htmlspecialchars($patient['address']); ?></textarea>
                </div>

                <div class="form-group">
                    <label for="diagnosis" class="form-label">Diagnosis</label>
                    <textarea id="diagnosis" name="diagnosis" placeholder="Enter patient diagnosis (if applicable)" class="form-control"><?php echo htmlspecialchars($patient['diagnosis'] ?? ''); ?></textarea>
                </div>

                <div class="form-group">
                    <label for="purpose" class="form-label">Purpose</label>
                    <textarea id="purpose" name="purpose" required placeholder="Enter purpose for medical certificate" class="form-control"><?php echo htmlspecialchars($patient['purpose']); ?></textarea>
                </div>

                <div class="form-group">
                    <label for="barangay" class="form-label">Facility/Barangay</label>
                    <?php if($current_user['role'] === 'super_admin'): ?>
                    <select id="barangay" name="barangay" required class="form-control">
                        <option value="">Select Barangay</option>
                        <?php
                        $barangays = [
                            'Baclaran', 'B. F. Homes', 'Don Bosco', 'Don Galo', 'La Huerta',
                            'Merville', 'Moonwalk', 'San Antonio', 'San Dionisio', 'San Isidro',
                            'San Martin de Porres', 'Marcelo Green', 'Sto. Niño', 'Sun Valley', 'Tambo',
                            'Vitalez'
                        ];
                        sort($barangays);
                        foreach($barangays as $barangay_option) {
                            echo '<option value="' . htmlspecialchars($barangay_option) . '"';
                            if($barangay_option === $patient['barangay']) {
                                echo ' selected';
                            }
                            echo '>' . htmlspecialchars($barangay_option) . '</option>';
                        }
                        ?>
                    </select>
                    <?php else: ?>
                    <input type="text" class="form-control" value="<?php echo htmlspecialchars($current_user['barangay']); ?>" readonly>
                    <input type="hidden" name="barangay" value="<?php echo htmlspecialchars($current_user['barangay']); ?>">
                    <?php endif; ?>
                </div>

                <button type="submit" class="btn-submit">Update Patient</button>
            </form>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.querySelector('form');
            const fullnameInput = document.getElementById('fullname');

            // Add focus highlight
            fullnameInput.addEventListener('focus', function() {
                this.style.backgroundColor = '#fffde7'; // Light yellow background
            });

            // Remove highlight on blur
            fullnameInput.addEventListener('blur', function() {
                this.style.backgroundColor = '';
            });

            // Live validation as user types
            fullnameInput.addEventListener('input', function() {
                const fullname = this.value.trim();
                if (fullname === '') {
                    // No validation for empty input
                    this.style.borderColor = '';
                } else if (fullname.includes(',')) {
                    // Valid format
                    this.style.borderColor = '#4caf50'; // Green border
                } else {
                    // Invalid format
                    this.style.borderColor = '#f44336'; // Red border
                }
            });

            // Form submission validation
            form.addEventListener('submit', function(e) {
                const fullname = fullnameInput.value.trim();

                // Check if the fullname contains a comma
                if (!fullname.includes(',')) {
                    e.preventDefault();
                    alert('Please use the correct format for Full Name: Last Name, First Name Middle Name. Example: Dela Cruz, Juan Martinez');
                    fullnameInput.focus();
                }
            });
        });
    </script>
</body>
</html>