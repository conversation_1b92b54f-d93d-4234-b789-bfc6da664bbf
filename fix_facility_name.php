<?php
session_start();
include 'config.php';

// Check if running from command line
$is_cli = (php_sapi_name() === 'cli');

// Only proceed if running from CLI or logged in as admin
if(!$is_cli && (!isset($_SESSION['user_id']) || ($_SESSION['role'] !== 'super_admin' && $_SESSION['role'] !== 'admin'))) {
    echo "Access denied. Only administrators can run this script.";
    exit;
}

// Update the facility name for user with username 'PJ'
$sql = "UPDATE users SET barangay = 'BF HOMES HEALTH CENTER' WHERE username = 'PJ'";

if ($conn->query($sql) === TRUE) {
    echo "Facility name for user 'PJ' has been updated successfully!";
} else {
    echo "Error updating record: " . $conn->error;
}

$conn->close();
?> 