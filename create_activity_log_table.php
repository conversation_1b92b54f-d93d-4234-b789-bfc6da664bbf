<?php
session_start();
include 'config.php';

// Check if user is logged in and is admin or super_admin
if(isset($_SESSION['user_id'])) {
    $user_id = $_SESSION['user_id'];
    $check_admin = "SELECT role FROM users WHERE id = ?";
    $stmt = $conn->prepare($check_admin);
    $stmt->bind_param("i", $user_id);
    $stmt->execute();
    $result = $stmt->get_result();
    $current_user = $result->fetch_assoc();
    
    if($current_user['role'] !== 'admin' && $current_user['role'] !== 'super_admin') {
        echo "Access denied. Only admins can run this script.";
        exit();
    }
}

// Create activity_log table if it doesn't exist
$sql = "CREATE TABLE IF NOT EXISTS activity_log (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    activity_type VARCHAR(50) NOT NULL,
    details TEXT,
    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
)";

if ($conn->query($sql) === TRUE) {
    echo "Activity log table created successfully or already exists!";
} else {
    echo "Error creating activity_log table: " . $conn->error;
}

// For backward compatibility, also ensure user_activity table exists
// This is used by activity_logger.php
$sql2 = "CREATE TABLE IF NOT EXISTS user_activity (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    action VARCHAR(50) NOT NULL,
    details TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
)";

if ($conn->query($sql2) === TRUE) {
    echo "<br>User activity table created successfully or already exists!";
} else {
    echo "<br>Error creating user_activity table: " . $conn->error;
}

$conn->close();
?> 