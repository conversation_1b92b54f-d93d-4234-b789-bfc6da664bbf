<?php
session_start();
include 'config.php';

// Check if user is logged in and is admin
if(!isset($_SESSION['user_id']) || ($_SESSION['role'] !== 'super_admin' && $_SESSION['role'] !== 'admin')) {
    header("Location: login.php");
    exit();
}

// First check if the column already exists
$check_column = "SHOW COLUMNS FROM patients LIKE 'diagnosis'";
$result = $conn->query($check_column);

if($result->num_rows == 0) {
    // Column doesn't exist, so add it
    $sql = "ALTER TABLE patients ADD COLUMN diagnosis TEXT NULL AFTER purpose";

    if ($conn->query($sql) === TRUE) {
        echo "<div style='padding: 20px; background-color: #d4edda; color: #155724; border-radius: 5px; margin: 20px;'>
              <h3>Success!</h3>
              <p>Diagnosis column added successfully to patients table.</p>
              </div>";
    } else {
        echo "<div style='padding: 20px; background-color: #f8d7da; color: #721c24; border-radius: 5px; margin: 20px;'>
              <h3>Error</h3>
              <p>Error adding diagnosis column: " . $conn->error . "</p>
              </div>";
    }
} else {
    echo "<div style='padding: 20px; background-color: #cce5ff; color: #004085; border-radius: 5px; margin: 20px;'>
          <h3>Information</h3>
          <p>The 'diagnosis' column already exists in the patients table.</p>
          </div>";
}

echo "<div style='text-align: center; margin-top: 20px;'>
      <a href='dashboard.php' style='padding: 10px 15px; background-color: #2e7d32; color: white; text-decoration: none; border-radius: 4px;'>
      Back to Dashboard</a>
      </div>";
?> 