<?php
/**
 * Patient Access Check
 * 
 * This script is included at the beginning of any page that displays patient details.
 * It ensures that users only have access to patients from their assigned facility.
 */

session_start();
include_once 'config.php';
include_once 'facility_access_handler.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header("Location: login.php");
    exit();
}

// Check if patient ID is provided
if (!isset($_GET['id'])) {
    $_SESSION['error'] = "No patient specified.";
    header("Location: view_patients.php");
    exit();
}

$patient_id = $_GET['id'];
$user_id = $_SESSION['user_id'];

// Check if user has access to this patient
if (!userHasAccessToPatient($conn, $user_id, $patient_id)) {
    // Get user and patient information for logging
    $user_query = "SELECT username FROM users WHERE id = ?";
    $user_stmt = $conn->prepare($user_query);
    $user_stmt->bind_param("i", $user_id);
    $user_stmt->execute();
    $user_result = $user_stmt->get_result();
    $user_data = $user_result->fetch_assoc();
    
    $patient_query = "SELECT control_number, barangay FROM patients WHERE id = ?";
    $patient_stmt = $conn->prepare($patient_query);
    $patient_stmt->bind_param("i", $patient_id);
    $patient_stmt->execute();
    $patient_result = $patient_stmt->get_result();
    $patient_data = $patient_result->fetch_assoc();
    
    $details = "User '" . $user_data['username'] . "' attempted to access patient #" . 
               $patient_data['control_number'] . " from " . $patient_data['barangay'];
               
    // First check if activity_log table exists
    $check_table_sql = "SHOW TABLES LIKE 'activity_log'";
    $result = $conn->query($check_table_sql);
    
    if ($result->num_rows == 0) {
        // Table doesn't exist, try to create it
        $create_table_sql = "CREATE TABLE IF NOT EXISTS activity_log (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            activity_type VARCHAR(50) NOT NULL,
            details TEXT,
            timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
        )";
        $conn->query($create_table_sql);
    }
    
    try {
        // Log unauthorized access attempt
        $log_sql = "INSERT INTO activity_log (user_id, activity_type, details, timestamp) 
                    VALUES (?, 'Unauthorized Access', ?, NOW())";
        $stmt = $conn->prepare($log_sql);
        $stmt->bind_param("is", $user_id, $details);
        $stmt->execute();
    } catch (Exception $e) {
        // If there's still an error, log to PHP error log but don't break the application
        error_log("Error logging unauthorized access: " . $e->getMessage());
        
        // Try using the user_activity table as fallback
        try {
            $fallback_sql = "INSERT INTO user_activity (user_id, action, details) VALUES (?, 'Unauthorized Access', ?)";
            $fallback_stmt = $conn->prepare($fallback_sql);
            $fallback_stmt->bind_param("is", $user_id, $details);
            $fallback_stmt->execute();
        } catch (Exception $e2) {
            error_log("Error in fallback activity logging: " . $e2->getMessage());
        }
    }
    
    // Redirect with error message
    $_SESSION['error'] = "You don't have permission to access this patient's records.";
    header("Location: view_patients.php");
    exit();
}

// If we get here, the user has access to the patient
// The script continues to the actual patient details page
?> 