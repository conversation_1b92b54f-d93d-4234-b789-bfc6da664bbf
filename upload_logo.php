<?php
session_start();
include 'config.php';

// Check if user is logged in and is admin
if(!isset($_SESSION['user_id']) || ($_SESSION['role'] !== 'super_admin' && $_SESSION['role'] !== 'admin')) {
    header("Location: login.php");
    exit();
}

$message = '';

if(isset($_POST['submit'])) {
    // Check if file was uploaded without errors
    if(isset($_FILES['logo']) && $_FILES['logo']['error'] === 0) {
        $allowed = ['jpg', 'jpeg', 'png', 'gif'];
        $filename = $_FILES['logo']['name'];
        $filesize = $_FILES['logo']['size'];
        $filetype = $_FILES['logo']['type'];
        
        // Get file extension
        $ext = strtolower(pathinfo($filename, PATHINFO_EXTENSION));
        
        // Verify file extension
        if(!in_array($ext, $allowed)) {
            $message = "Error: Please select a valid file format.";
        } else {
            // Verify file size - 2MB maximum
            if($filesize > 2097152) {
                $message = "Error: File size must be less than 2MB.";
            } else {
                // Create assets directory if it doesn't exist
                if(!file_exists('assets')) {
                    mkdir('assets', 0777, true);
                }
                
                // Save file to assets directory
                $newname = "alagang-paranaque-logo.$ext";
                $target = "assets/" . $newname;
                
                if(move_uploaded_file($_FILES['logo']['tmp_name'], $target)) {
                    $message = "Success: Logo uploaded successfully!";
                } else {
                    $message = "Error: There was a problem uploading the file.";
                }
            }
        }
    } else {
        $message = "Error: " . $_FILES['logo']['error'];
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Upload Logo</title>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: 'Poppins', sans-serif;
            background: linear-gradient(135deg, #e8f5e9 0%, #c8e6c9 100%);
            margin: 0;
            padding: 0;
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        
        .container {
            background: white;
            border-radius: 8px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            width: 100%;
            max-width: 500px;
            padding: 30px;
        }
        
        h1 {
            color: #2e7d32;
            font-size: 24px;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .message {
            padding: 10px;
            margin-bottom: 20px;
            border-radius: 4px;
            text-align: center;
        }
        
        .success {
            background-color: #d4edda;
            color: #155724;
        }
        
        .error {
            background-color: #f8d7da;
            color: #721c24;
        }
        
        form {
            display: flex;
            flex-direction: column;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        label {
            display: block;
            font-size: 14px;
            color: #333;
            margin-bottom: 5px;
        }
        
        input[type="file"] {
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 8px;
            width: 100%;
        }
        
        .btn {
            background: #2e7d32;
            color: white;
            border: none;
            border-radius: 4px;
            padding: 10px 15px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .btn:hover {
            background: #357a38;
        }
        
        .back-link {
            display: block;
            text-align: center;
            margin-top: 20px;
            color: #2e7d32;
            text-decoration: none;
        }
        
        .back-link:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Upload Logo</h1>
        
        <?php if(!empty($message)): ?>
            <div class="message <?php echo strpos($message, 'Success') !== false ? 'success' : 'error'; ?>">
                <?php echo $message; ?>
            </div>
        <?php endif; ?>
        
        <form action="" method="post" enctype="multipart/form-data">
            <div class="form-group">
                <label for="logo">Select Logo Image (JPG, PNG, GIF)</label>
                <input type="file" name="logo" id="logo" accept=".jpg, .jpeg, .png, .gif" required>
            </div>
            
            <button type="submit" name="submit" class="btn">Upload Logo</button>
        </form>
        
        <a href="dashboard.php" class="back-link">Back to Dashboard</a>
    </div>
</body>
</html> 