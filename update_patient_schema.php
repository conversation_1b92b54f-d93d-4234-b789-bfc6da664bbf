<?php
include 'config.php';

// Check if diagnosis column exists in patients table
$check_column = "SHOW COLUMNS FROM patients LIKE 'diagnosis'";
$result = $conn->query($check_column);

if ($result->num_rows == 0) {
    echo "<h2>Adding Diagnosis Column:</h2>";
    $sql = "ALTER TABLE patients ADD COLUMN diagnosis TEXT DEFAULT NULL AFTER barangay";
    if ($conn->query($sql) === TRUE) {
        echo "<p style='color: green;'>Column 'diagnosis' added successfully to patients table.</p>";
    } else {
        echo "<p style='color: red;'>Error adding column: " . $conn->error . "</p>";
    }
} else {
    echo "<h2>Diagnosis Column Status:</h2>";
    echo "<p style='color: blue;'>Column 'diagnosis' already exists in patients table.</p>";
}

// Display current patients table structure
echo "<h2>Current Patients Table Structure:</h2>";
$show_table = "DESCRIBE patients";
$result = $conn->query($show_table);
echo "<table border='1'>";
echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
while($row = $result->fetch_assoc()) {
    echo "<tr>";
    echo "<td>".$row['Field']."</td>";
    echo "<td>".$row['Type']."</td>";
    echo "<td>".$row['Null']."</td>";
    echo "<td>".$row['Key']."</td>";
    echo "<td>".$row['Default']."</td>";
    echo "<td>".$row['Extra']."</td>";
    echo "</tr>";
}
echo "</table>";

// Display sample patient data
echo "<h2>Sample Patient Data:</h2>";
$show_patients = "SELECT id, control_number, fullname, diagnosis FROM patients LIMIT 5";
$result = $conn->query($show_patients);
echo "<table border='1'>";
echo "<tr><th>ID</th><th>Control Number</th><th>Full Name</th><th>Diagnosis</th></tr>";
while($row = $result->fetch_assoc()) {
    echo "<tr>";
    echo "<td>".$row['id']."</td>";
    echo "<td>".$row['control_number']."</td>";
    echo "<td>".$row['fullname']."</td>";
    echo "<td>".($row['diagnosis'] ?? 'NULL')."</td>";
    echo "</tr>";
}
echo "</table>";

$conn->close();
?> 