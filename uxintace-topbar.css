/* Uxintace Topbar Styles with Color Palette from Image */
@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Color Palette from Image */
:root {
    --pink: #FFAEBC;
    --mint: #A0E7E5;
    --light-green: #B4F8C8;
    --light-peach: #FBE7C6;
}

.topbar {
    background: linear-gradient(135deg, var(--pink) 0%, var(--mint) 100%);
    padding: 12px 20px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 64px;
    animation: fadeInDown 0.5s ease-out forwards;
}

.logo-section {
    display: flex;
    align-items: center;
    height: 100%;
    padding-left: 20px;
}

.nav-menu {
    display: flex;
    align-items: center;
    height: 100%;
}

.nav-item {
    display: flex;
    align-items: center;
    padding: 0 15px;
    color: rgba(255, 255, 255, 0.9);
    text-decoration: none;
    border-radius: 6px;
    transition: all 0.3s ease;
    font-size: 14px;
    height: 40px;
    position: relative;
    margin-right: 5px;
    animation: slideInRight 0.5s ease-out forwards;
    animation-delay: calc(0.1s * var(--animation-order, 0));
    opacity: 0;
}

.nav-item:hover {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    transform: translateY(-2px);
}

.nav-item.active {
    background: rgba(255, 255, 255, 0.25);
    color: white;
    font-weight: 500;
}

.nav-item.active::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 15%;
    width: 70%;
    height: 3px;
    background-color: var(--light-green);
    border-radius: 3px 3px 0 0;
}

.nav-item i {
    margin-right: 8px;
    width: 16px;
    text-align: center;
    font-size: 14px;
    transition: transform 0.3s ease;
}

.nav-item:hover i {
    transform: scale(1.2);
}

.user-profile {
    display: flex;
    align-items: center;
    gap: 12px;
    animation: fadeInDown 0.5s ease-out forwards;
    animation-delay: 0.3s;
    opacity: 0;
}

.user-profile span {
    padding: 8px 16px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 30px;
    color: white;
    font-size: 13px;
    font-weight: 500;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.3);
    display: flex;
    align-items: center;
}

.user-profile span:before {
    content: '\f007';
    font-family: 'Font Awesome 5 Free';
    font-weight: 900;
    margin-right: 8px;
    font-size: 14px;
}

.user-profile span:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Adjust main content to account for fixed topbar */
.main-content {
    margin-top: 80px;
}

/* Button styles with new color palette */
.btn-primary {
    background: linear-gradient(135deg, var(--pink) 0%, var(--mint) 100%);
    color: white;
    border: none;
    border-radius: 30px;
    padding: 10px 20px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 15px rgba(0, 0, 0, 0.15);
}

.btn-secondary {
    background: linear-gradient(135deg, var(--light-green) 0%, var(--light-peach) 100%);
    color: #333;
    border: none;
    border-radius: 30px;
    padding: 10px 20px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}

.btn-secondary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 15px rgba(0, 0, 0, 0.15);
}

/* Media queries for responsive design */
@media (max-width: 768px) {
    .nav-menu {
        display: none;
    }
    
    .topbar {
        padding: 10px;
    }
    
    .logo-section {
        padding-left: 10px;
    }
}
