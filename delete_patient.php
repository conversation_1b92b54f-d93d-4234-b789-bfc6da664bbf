<?php
session_start();
include 'config.php';
include 'activity_logger.php';

// Check if user is logged in
if(!isset($_SESSION['user_id'])) {
    header("Location: login.php");
    exit();
}

// Get current user information
$user_id = $_SESSION['user_id'];
$check_user = "SELECT * FROM users WHERE id = ?";
$stmt = $conn->prepare($check_user);
$stmt->bind_param("i", $user_id);
$stmt->execute();
$result = $stmt->get_result();
$current_user = $result->fetch_assoc();

// Check if user is admin or super_admin
if($current_user['role'] !== 'admin' && $current_user['role'] !== 'super_admin') {
    $_SESSION['error'] = "You don't have permission to delete patients";
    header("Location: view_patients.php");
    exit();
}

// Check if patient ID is provided
if(!isset($_GET['id']) || empty($_GET['id'])) {
    $_SESSION['error'] = "No patient ID provided";
    header("Location: view_patients.php");
    exit();
}

$patient_id = (int)$_GET['id'];

// Get patient information before deleting (for logging)
$sql = "SELECT * FROM patients WHERE id = ?";
$stmt = $conn->prepare($sql);
$stmt->bind_param("i", $patient_id);
$stmt->execute();
$result = $stmt->get_result();
$patient = $result->fetch_assoc();

// Check if patient exists
if(!$patient) {
    $_SESSION['error'] = "Patient not found";
    header("Location: view_patients.php");
    exit();
}

// Check if user has access to this patient's facility
if($current_user['role'] !== 'super_admin' && $patient['barangay'] !== $current_user['barangay']) {
    $_SESSION['error'] = "You don't have permission to delete patients from other facilities";
    header("Location: view_patients.php");
    exit();
}

// Delete the patient
$sql = "DELETE FROM patients WHERE id = ?";
$stmt = $conn->prepare($sql);
$stmt->bind_param("i", $patient_id);

if($stmt->execute()) {
    // Log the activity
    $details = "Deleted patient: " . $patient['fullname'] . " (ID: " . $patient_id . ", Control Number: " . $patient['control_number'] . ")";
    logActivity($conn, $_SESSION['user_id'], "Delete Patient", $details);
    
    $_SESSION['success'] = "Patient deleted successfully";
} else {
    $_SESSION['error'] = "Error deleting patient: " . $conn->error;
}

header("Location: view_patients.php");
exit();
?> 