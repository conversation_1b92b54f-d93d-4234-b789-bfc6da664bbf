/* Enhanced Topbar Styles with Animations */
@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(46, 125, 50, 0.4);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(46, 125, 50, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(46, 125, 50, 0);
    }
}

@keyframes ripple {
    0% {
        transform: scale(0);
        opacity: 0.5;
    }
    100% {
        transform: scale(2);
        opacity: 0;
    }
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-5px);
    }
    60% {
        transform: translateY(-2px);
    }
}

/* Ripple effect for buttons */
.ripple-effect {
    position: absolute;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.4);
    transform: scale(0);
    animation: ripple 0.6s linear;
    pointer-events: none;
}

/* Animation for icons */
.nav-item i.animated {
    animation: bounce 0.5s ease;
}

/* Position relative for ripple effect */
.nav-item, .user-profile span, .logout-btn {
    position: relative;
    overflow: hidden;
}

.topbar {
    background: linear-gradient(135deg, #2e7d32 0%, #388e3c 100%);
    padding: 12px 20px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 64px;
    animation: fadeInDown 0.5s ease-out forwards;
}

.logo-section {
    display: flex;
    align-items: center;
    height: 100%;
    padding-left: 20px;
}

.nav-menu {
    display: flex;
    align-items: center;
    height: 100%;
}

.nav-item {
    display: flex;
    align-items: center;
    padding: 0 15px;
    color: rgba(255, 255, 255, 0.85);
    text-decoration: none;
    border-radius: 6px;
    transition: all 0.3s ease;
    font-size: 14px;
    height: 40px;
    position: relative;
    margin-right: 5px;
    animation: slideInRight 0.5s ease-out forwards;
    animation-delay: calc(0.1s * var(--animation-order, 0));
    opacity: 0;
}

.nav-item:hover {
    background: rgba(255, 255, 255, 0.15);
    color: white;
    transform: translateY(-2px);
}

.nav-item.active {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    font-weight: 500;
}

.nav-item.active::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 15%;
    width: 70%;
    height: 3px;
    background-color: white;
    border-radius: 3px 3px 0 0;
}

.nav-item i {
    margin-right: 8px;
    width: 16px;
    text-align: center;
    font-size: 14px;
    transition: transform 0.3s ease;
}

.nav-item:hover i {
    transform: scale(1.2);
}

.user-profile {
    display: flex;
    align-items: center;
    gap: 12px;
    animation: fadeInDown 0.5s ease-out forwards;
    animation-delay: 0.3s;
    opacity: 0;
}

.user-profile span {
    padding: 8px 16px;
    background: rgba(255, 255, 255, 0.15);
    border-radius: 30px;
    color: white;
    font-size: 13px;
    font-weight: 500;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.2);
    display: flex;
    align-items: center;
}

.user-profile span:before {
    content: '\f007';
    font-family: 'Font Awesome 5 Free';
    font-weight: 900;
    margin-right: 8px;
    font-size: 14px;
}

.user-profile span:hover {
    background: rgba(255, 255, 255, 0.25);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    animation: pulse 1.5s infinite;
}

.logout-btn {
    padding: 8px 16px;
    background: rgba(220, 53, 69, 0.1);
    border-radius: 30px;
    color: white;
    text-decoration: none;
    font-size: 13px;
    font-weight: 500;
    transition: all 0.3s ease;
    border: 1px solid rgba(220, 53, 69, 0.2);
    display: flex;
    align-items: center;
}

.logout-btn:before {
    content: '\f2f5';
    font-family: 'Font Awesome 5 Free';
    font-weight: 900;
    margin-right: 8px;
    font-size: 14px;
}

.logout-btn:hover {
    background: rgba(220, 53, 69, 0.8);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(220, 53, 69, 0.3);
}

/* Adjust main content to account for fixed topbar */
.main-content {
    margin-top: 80px;
}
