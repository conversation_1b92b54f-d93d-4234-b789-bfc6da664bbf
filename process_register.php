<?php
session_start();
include 'config.php';
include 'facility_access_handler.php';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Get form data
    $username = $_POST['username'];
    $password = $_POST['password'];
    $fullname = $_POST['fullname'];
    $designation = $_POST['designation'];
    $barangay = $_POST['barangay'];
    $role = 'staff'; // Default role for new registrations
    $status = 'pending'; // Set initial status to pending

    // Validate required fields
    if (empty($username) || empty($password) || empty($fullname) || empty($designation) || empty($barangay)) {
        $_SESSION['register_error'] = "All fields are required!";
        header("Location: register.php");
        exit();
    }

    // Check if username already exists
    $check_sql = "SELECT id FROM users WHERE username = ?";
    $check_stmt = $conn->prepare($check_sql);
    $check_stmt->bind_param("s", $username);
    $check_stmt->execute();
    $result = $check_stmt->get_result();

    if ($result->num_rows > 0) {
        $_SESSION['register_error'] = "Username already exists!";
        header("Location: register.php");
        exit();
    }

    // Hash the password
    $hashed_password = password_hash($password, PASSWORD_DEFAULT);

    // Insert new user with pending status
    $sql = "INSERT INTO users (username, password, fullname, designation, barangay, role, status, created_at) 
            VALUES (?, ?, ?, ?, ?, ?, ?, NOW())";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("sssssss", $username, $hashed_password, $fullname, $designation, $barangay, $role, $status);

    if ($stmt->execute()) {
        // Get the new user's ID
        $new_user_id = $conn->insert_id;
        
        // Count patients in the selected facility
        $patient_count = countPatientsInFacility($conn, $barangay);
        
        // Set session variables for the completion page
        $_SESSION['registration_complete'] = true;
        $_SESSION['registered_username'] = $username;
        
        // If registered by super admin, redirect to pending section
        if (isset($_SESSION['role']) && $_SESSION['role'] === 'super_admin') {
            $_SESSION['success'] = "User registered successfully! Once approved, they will have access to " . 
                                  $patient_count . " patient record" . ($patient_count != 1 ? "s" : "") . 
                                  " in " . $barangay . ".";
            header("Location: manage_users.php#pending-section");
        } else {
            // For regular registration, redirect to completion page with facility info
            $_SESSION['facility_patient_count'] = $patient_count;
            $_SESSION['selected_facility'] = $barangay;
            header("Location: registration_complete.php");
        }
        exit();
    } else {
        $_SESSION['register_error'] = "Registration failed. Please try again.";
        header("Location: register.php");
        exit();
    }

    $stmt->close();
    $check_stmt->close();
} else {
    header("Location: register.php");
    exit();
}

$conn->close();
?> 