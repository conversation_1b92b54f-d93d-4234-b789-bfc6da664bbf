<?php
session_start();
include 'config.php';
include 'activity_logger.php';

// Check if user is logged in
if(!isset($_SESSION['user_id'])) {
    header("Location: login.php");
    exit();
}

// Get current user information
$user_id = $_SESSION['user_id'];
$check_user = "SELECT * FROM users WHERE id = ?";
$stmt = $conn->prepare($check_user);
$stmt->bind_param("i", $user_id);
$stmt->execute();
$result = $stmt->get_result();
$current_user = $result->fetch_assoc();

$username = $_SESSION['username'];

// Get patient ID from URL
$patient_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

// Get patient information
if ($patient_id > 0) {
    $sql = "SELECT * FROM patients WHERE id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("i", $patient_id);
    $stmt->execute();
    $result = $stmt->get_result();
    $patient = $result->fetch_assoc();

    if (!$patient) {
        header("Location: view_patients.php");
        exit();
    }
}

// Handle prescription deletion
if(isset($_POST['delete_prescription']) && isset($_POST['prescription_id'])) {
    $prescription_id = (int)$_POST['prescription_id'];

    // Check if prescription exists and belongs to this patient
    $check_sql = "SELECT p.*, patient.fullname FROM prescriptions p
                  JOIN patients patient ON p.patient_id = patient.id
                  WHERE p.id = ? AND p.patient_id = ?";
    $check_stmt = $conn->prepare($check_sql);
    $check_stmt->bind_param("ii", $prescription_id, $patient_id);
    $check_stmt->execute();
    $check_result = $check_stmt->get_result();

    if($check_result->num_rows > 0) {
        $prescription_data = $check_result->fetch_assoc();

        // Delete the prescription
        $delete_sql = "DELETE FROM prescriptions WHERE id = ?";
        $delete_stmt = $conn->prepare($delete_sql);
        $delete_stmt->bind_param("i", $prescription_id);

        if($delete_stmt->execute()) {
            // Log the activity
            $details = "Deleted prescription (ID: " . $prescription_id . ") for patient: " . $prescription_data['fullname'];
            logActivity($conn, $user_id, "Delete Prescription", $details);

            $_SESSION['success'] = "Prescription deleted successfully.";
        } else {
            $_SESSION['error'] = "Error deleting prescription.";
        }

        // Redirect to refresh the page
        header("Location: view_prescriptions.php?id=" . $patient_id);
        exit();
    }
}

// Check if prescriptions table exists, if not create it
$table_check = $conn->query("SHOW TABLES LIKE 'prescriptions'");
if($table_check->num_rows == 0) {
    $create_table = "CREATE TABLE prescriptions (
        id INT(11) NOT NULL AUTO_INCREMENT,
        patient_id INT(11) NOT NULL,
        medications TEXT NOT NULL,
        dosage TEXT NOT NULL,
        instructions TEXT NOT NULL,
        prescription_date DATE NOT NULL,
        created_by INT(11) NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (id),
        KEY patient_id (patient_id)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";

    $conn->query($create_table);
}

// Get prescriptions for this patient
$prescriptions = [];
if($patient_id > 0) {
    $prescriptions_sql = "SELECT p.*, u.username as prescribed_by FROM prescriptions p
                         LEFT JOIN users u ON p.created_by = u.id
                         WHERE p.patient_id = ?
                         ORDER BY p.prescription_date DESC, p.id DESC";
    $prescriptions_stmt = $conn->prepare($prescriptions_sql);
    $prescriptions_stmt->bind_param("i", $patient_id);
    $prescriptions_stmt->execute();
    $prescriptions_result = $prescriptions_stmt->get_result();

    while($row = $prescriptions_result->fetch_assoc()) {
        $prescriptions[] = $row;
    }
}

// Success and error messages
$success_message = isset($_SESSION['success']) ? $_SESSION['success'] : '';
$error_message = isset($_SESSION['error']) ? $_SESSION['error'] : '';

// Clear session messages
unset($_SESSION['success']);
unset($_SESSION['error']);
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>View Prescriptions</title>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Poppins', sans-serif;
        }

        body {
            font-family: 'Poppins', sans-serif;
            background: linear-gradient(135deg, #e8f5e9 0%, #c8e6c9 100%);
            margin: 0;
            padding: 0;
            min-height: 100vh;
        }

        /* Topbar Styles */
        .topbar {
            background: white;
            padding: 12px 20px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            display: flex;
            justify-content: space-between;
            align-items: center;
            height: 64px;
        }

        .logo-section {
            display: flex;
            align-items: center;
            height: 100%;
        }

        .logo {
            color: #2e7d32;
            font-size: 20px;
            font-weight: 600;
            display: flex;
            align-items: center;
            text-decoration: none;
            height: 100%;
            padding-right: 20px;
            margin-right: 20px;
            border-right: 1px solid #f0f0f0;
        }

        .logo-image {
            height: 40px;
            width: auto;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 3px;
        }

        .logo-text {
            display: flex;
            flex-direction: column;
        }

        .logo-title {
            color: #2e7d32;
            font-size: 18px;
            font-weight: 600;
            line-height: 1.1;
        }

        .logo-subtitle {
            color: #47a84e;
            font-size: 12px;
            font-weight: 500;
        }

        .nav-menu {
            display: flex;
            align-items: center;
            height: 100%;
        }

        .nav-item {
            display: flex;
            align-items: center;
            padding: 0 15px;
            color: #666;
            text-decoration: none;
            border-radius: 6px;
            transition: all 0.2s ease;
            font-size: 14px;
            height: 40px;
        }

        .nav-item:hover {
            background: #f0f7f0;
            color: #2e7d32;
        }

        .nav-item.active {
            background: #e8f5e9;
            color: #2e7d32;
            font-weight: 500;
        }

        .nav-item i {
            margin-right: 8px;
            width: 16px;
            text-align: center;
            font-size: 14px;
        }

        /* Main Content Styles */
        .main-content {
            margin-top: 60px;
            padding: 20px;
            width: 100%;
            box-sizing: border-box;
        }

        .user-profile {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .user-profile span {
            padding: 6px 12px;
            background: white;
            border-radius: 4px;
            color: #2e7d32;
            font-size: 13px;
            font-weight: 500;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
        }

        /* Container Styles */
        .container {
            background: white;
            border-radius: 8px;
            padding: 25px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
            max-width: 1000px;
            margin: 0 auto;
        }

        .container-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .container-title {
            font-size: 18px;
            color: #333;
            font-weight: 500;
        }

        .btn {
            display: inline-block;
            padding: 8px 16px;
            background: #2e7d32;
            color: white;
            border-radius: 4px;
            text-decoration: none;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.2s ease;
            border: none;
            cursor: pointer;
        }

        .btn:hover {
            background: #1b5e20;
        }

        .back-btn {
            display: inline-block;
            margin-bottom: 20px;
            padding: 8px 16px;
            background: #f0f7f0;
            color: #2e7d32;
            border-radius: 4px;
            text-decoration: none;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.2s ease;
        }

        .back-btn i {
            margin-right: 5px;
        }

        .back-btn:hover {
            background: #e8f5e9;
        }

        /* Patient Info Styles */
        .patient-info {
            background-color: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
            border-left: 4px solid #2e7d32;
        }

        .patient-info p {
            margin: 5px 0;
            font-size: 14px;
            color: #444;
        }

        .patient-info strong {
            font-weight: 600;
            color: #333;
        }

        /* Prescription Cards */
        .prescription-list {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .prescription-card {
            background: #f9f9f9;
            border-radius: 8px;
            padding: 15px;
            border-left: 4px solid #4caf50;
            position: relative;
        }

        .prescription-date {
            font-size: 12px;
            color: #666;
            margin-bottom: 10px;
        }

        .prescription-section {
            margin-bottom: 12px;
        }

        .prescription-section h4 {
            font-size: 14px;
            color: #333;
            margin-bottom: 5px;
            font-weight: 500;
        }

        .prescription-section p {
            font-size: 13px;
            color: #555;
            white-space: pre-line;
            line-height: 1.4;
        }

        .prescribed-by {
            font-size: 12px;
            color: #777;
            font-style: italic;
            margin-top: 10px;
            text-align: right;
        }

        .no-records {
            text-align: center;
            padding: 30px;
            color: #666;
            font-size: 14px;
        }

        /* Action buttons */
        .action-buttons {
            position: absolute;
            top: 10px;
            right: 10px;
            display: flex;
            gap: 5px;
        }

        .action-btn {
            background: none;
            border: none;
            cursor: pointer;
            font-size: 16px;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 4px;
            transition: all 0.2s;
        }

        .print-btn {
            color: #1976d2;
        }

        .print-btn:hover {
            background: #e3f2fd;
        }

        .delete-btn {
            color: #d32f2f;
        }

        .delete-btn:hover {
            background: #ffebee;
        }

        /* Messages */
        .success-message {
            background-color: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }

        .error-message {
            background-color: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <!-- Topbar -->
    <div class="topbar">
        <div class="logo-section">

            <nav class="nav-menu">
                <a href="dashboard.php" class="nav-item">
                    <i class="fas fa-home"></i>
                    Dashboard
                </a>
                <?php if($current_user['role'] === 'super_admin' || $current_user['role'] === 'admin' || $current_user['role'] === 'staff'): ?>
                <a href="add_patient.php" class="nav-item">
                    <i class="fas fa-user-plus"></i>
                    Add Patient
                </a>
                <?php endif; ?>
                <a href="view_patients.php" class="nav-item">
                    <i class="fas fa-users"></i>
                    Patient Records
                </a>
                <?php if($current_user['role'] !== 'staff'): ?>
                <a href="manage_users.php" class="nav-item">
                    <i class="fas fa-users-cog"></i>
                    User Management
                </a>
                <?php endif; ?>
                <a href="logout.php" class="nav-item">
                    <i class="fas fa-sign-out-alt"></i>
                    Logout
                </a>
            </nav>
        </div>
        <div class="user-profile">
            <span><?php echo htmlspecialchars($username); ?></span>
        </div>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <div class="container">
            <a href="view_patients.php" class="back-btn">
                <i class="fas fa-arrow-left"></i> Back to Patients
            </a>

            <?php if(!empty($success_message)): ?>
                <div class="success-message"><?php echo $success_message; ?></div>
            <?php endif; ?>

            <?php if(!empty($error_message)): ?>
                <div class="error-message"><?php echo $error_message; ?></div>
            <?php endif; ?>

            <div class="container-header">
                <h2 class="container-title"><?php echo isset($patient) ? 'Prescriptions for ' . htmlspecialchars($patient['fullname']) : 'All Prescriptions'; ?></h2>
                <?php if(isset($patient) && ($current_user['role'] === 'super_admin' || $current_user['role'] === 'admin' || $current_user['role'] === 'staff')): ?>
                <a href="add_prescription.php?id=<?php echo $patient_id; ?>" class="btn">
                    <i class="fas fa-plus"></i> Add Prescription
                </a>
                <?php endif; ?>
            </div>

            <?php if(isset($patient)): ?>
            <div class="patient-info">
                <p><strong>Patient:</strong> <?php echo htmlspecialchars($patient['fullname']); ?></p>
                <p><strong>Age:</strong> <?php echo htmlspecialchars($patient['age']); ?> years</p>
                <p><strong>Sex:</strong> <?php echo htmlspecialchars($patient['sex']); ?></p>
                <p><strong>Address:</strong> <?php echo htmlspecialchars($patient['address']); ?></p>
                <p><strong>Facility/Barangay:</strong> <?php echo htmlspecialchars($patient['barangay']); ?></p>
            </div>
            <?php endif; ?>

            <?php if(!empty($prescriptions)): ?>
                <div class="prescription-list">
                    <?php foreach($prescriptions as $prescription): ?>
                        <div class="prescription-card">
                            <div class="prescription-date">
                                <i class="far fa-calendar-alt"></i> <?php echo date('F j, Y', strtotime($prescription['prescription_date'])); ?>
                            </div>

                            <div class="action-buttons">
                                <button type="button" onclick="printPrescription(<?php echo $prescription['id']; ?>)" class="action-btn print-btn" title="Print Prescription">
                                    <i class="fas fa-print"></i>
                                </button>
                                <?php if($current_user['role'] === 'super_admin' || $current_user['role'] === 'admin'): ?>
                                <button type="button" onclick="deletePrescription(<?php echo $prescription['id']; ?>)" class="action-btn delete-btn" title="Delete Prescription">
                                    <i class="fas fa-trash-alt"></i>
                                </button>
                                <?php endif; ?>
                            </div>

                            <div class="prescription-section">
                                <h4>Medications:</h4>
                                <p><?php echo nl2br(htmlspecialchars($prescription['medications'])); ?></p>
                            </div>

                            <div class="prescription-section">
                                <h4>Dosage:</h4>
                                <p><?php echo nl2br(htmlspecialchars($prescription['dosage'])); ?></p>
                            </div>

                            <?php if(!empty($prescription['instructions'])): ?>
                            <div class="prescription-section">
                                <h4>Special Instructions:</h4>
                                <p><?php echo nl2br(htmlspecialchars($prescription['instructions'])); ?></p>
                            </div>
                            <?php endif; ?>

                            <div class="prescribed-by">
                                Prescribed by: <?php echo htmlspecialchars($prescription['prescribed_by']); ?> on <?php echo date('M j, Y', strtotime($prescription['created_at'])); ?>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php else: ?>
                <div class="no-records">
                    <i class="fas fa-prescription fa-2x" style="color: #ccc; margin-bottom: 10px;"></i>
                    <p>No prescriptions found for this patient.</p>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Hidden form for delete action -->
    <form id="delete-form" action="" method="POST" style="display: none;">
        <input type="hidden" name="delete_prescription" value="1">
        <input type="hidden" name="prescription_id" id="prescription-id-input">
    </form>

    <script>
        function deletePrescription(prescriptionId) {
            if(confirm('Are you sure you want to delete this prescription? This action cannot be undone.')) {
                document.getElementById('prescription-id-input').value = prescriptionId;
                document.getElementById('delete-form').submit();
            }
        }

        function printPrescription(prescriptionId) {
            // Redirect to print_prescription.php with the prescription ID
            window.open('print_prescription.php?id=' + prescriptionId, '_blank');
        }
    </script>
</body>
</html>