<?php
session_start();

// Redirect if not coming from registration
if (!isset($_SESSION['registration_complete'])) {
    header("Location: register.php");
    exit();
}

// Get username if available
$username = isset($_SESSION['registered_username']) ? $_SESSION['registered_username'] : "";

// Get facility information if available
$facility = isset($_SESSION['selected_facility']) ? $_SESSION['selected_facility'] : "";
$patient_count = isset($_SESSION['facility_patient_count']) ? $_SESSION['facility_patient_count'] : 0;

// Clear the session variables
unset($_SESSION['registration_complete']);
unset($_SESSION['registered_username']);
unset($_SESSION['selected_facility']);
unset($_SESSION['facility_patient_count']);
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Registration Complete</title>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            background: linear-gradient(135deg, #e8f5e9 0%, #c8e6c9 100%);
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            font-family: 'Poppins', sans-serif;
            padding: 20px;
            margin: 0;
        }
        
        .completion-container {
            background: white;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            width: 90%;
            max-width: 500px;
            text-align: center;
        }
        
        .header {
            background: linear-gradient(135deg, #2e7d32 0%, #388e3c 100%);
            color: white;
            padding: 20px;
        }
        
        .header i {
            font-size: 50px;
            margin-bottom: 10px;
        }
        
        .header h1 {
            font-size: 24px;
            margin: 0;
        }
        
        .content {
            padding: 30px;
        }
        
        .message-box {
            background: #e8f5e9;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 25px;
        }
        
        .message-box h2 {
            color: #2e7d32;
            font-size: 18px;
            margin-top: 0;
            margin-bottom: 15px;
        }
        
        .message-box p {
            color: #333;
            font-size: 14px;
            line-height: 1.6;
            margin: 0 0 10px 0;
        }
        
        .notice {
            background: #fff3e0;
            border-left: 4px solid #f57c00;
            padding: 15px;
            border-radius: 4px;
            text-align: left;
            margin-bottom: 25px;
        }
        
        .notice h3 {
            color: #f57c00;
            font-size: 16px;
            display: flex;
            align-items: center;
            gap: 8px;
            margin-top: 0;
            margin-bottom: 10px;
        }
        
        .notice p {
            color: #555;
            font-size: 14px;
            line-height: 1.6;
            margin: 0;
        }
        
        .action-buttons {
            display: flex;
            gap: 15px;
            justify-content: center;
        }
        
        .btn {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 10px 20px;
            border-radius: 4px;
            text-decoration: none;
            font-weight: 500;
            font-size: 14px;
            transition: all 0.2s ease;
        }
        
        .primary-btn {
            background: #2e7d32;
            color: white;
        }
        
        .secondary-btn {
            background: #e8f5e9;
            color: #2e7d32;
            border: 1px solid #c8e6c9;
        }
        
        .primary-btn:hover {
            background: #1b5e20;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }
        
        .secondary-btn:hover {
            background: #c8e6c9;
            transform: translateY(-2px);
        }
        
        .steps {
            background: #f9f9f9;
            border-radius: 8px;
            padding: 20px;
            text-align: left;
            margin-bottom: 25px;
        }
        
        .steps h3 {
            color: #333;
            font-size: 16px;
            margin-top: 0;
            margin-bottom: 15px;
        }
        
        .step {
            display: flex;
            gap: 10px;
            align-items: flex-start;
            margin-bottom: 12px;
        }
        
        .step-number {
            background: #2e7d32;
            color: white;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
            flex-shrink: 0;
        }
        
        .step-text {
            font-size: 14px;
            color: #555;
            line-height: 1.5;
        }
    </style>
</head>
<body>
    <div class="completion-container">
        <div class="header">
            <i class="fas fa-check-circle"></i>
            <h1>Registration Complete</h1>
        </div>
        
        <div class="content">
            <div class="message-box">
                <h2>Thank you for registering!</h2>
                <p>Your account has been created successfully but requires approval before you can log in.</p>
                <?php if (!empty($username)): ?>
                <p><strong>Username:</strong> <?php echo htmlspecialchars($username); ?></p>
                <?php endif; ?>
            </div>
            
            <div class="notice">
                <h3><i class="fas fa-info-circle"></i> Pending Approval</h3>
                <p>Your account is currently in <strong>pending status</strong> and is awaiting approval from a system administrator. You will receive notification when your account has been approved.</p>
            </div>
            
            <?php if (!empty($facility)): ?>
            <div class="facility-info" style="background: #e3f2fd; border-radius: 8px; padding: 20px; margin-bottom: 25px; text-align: left;">
                <h3 style="color: #1976d2; font-size: 16px; display: flex; align-items: center; gap: 8px; margin-top: 0; margin-bottom: 10px;">
                    <i class="fas fa-hospital"></i> Facility Access
                </h3>
                <p style="color: #555; font-size: 14px; line-height: 1.6; margin: 0 0 10px 0;">
                    You have been assigned to <strong><?php echo htmlspecialchars($facility); ?></strong>.
                </p>
                <?php if ($patient_count > 0): ?>
                <p style="color: #555; font-size: 14px; line-height: 1.6; margin: 0;">
                    Once approved, you will have access to <strong><?php echo $patient_count; ?> patient record<?php echo ($patient_count != 1) ? 's' : ''; ?></strong> registered at this facility.
                </p>
                <?php else: ?>
                <p style="color: #555; font-size: 14px; line-height: 1.6; margin: 0;">
                    There are currently no patient records registered at this facility, but you will have access to any future records.
                </p>
                <?php endif; ?>
            </div>
            <?php endif; ?>
            
            <div class="steps">
                <h3>What happens next?</h3>
                <div class="step">
                    <div class="step-number">1</div>
                    <div class="step-text">Your registration has been submitted to the administrator for review.</div>
                </div>
                <div class="step">
                    <div class="step-number">2</div>
                    <div class="step-text">The administrator will verify your information and approve your account.</div>
                </div>
                <div class="step">
                    <div class="step-number">3</div>
                    <div class="step-text">Once approved, you will be able to log in with your credentials.</div>
                </div>
            </div>
            
            <div class="action-buttons">
                <a href="login.php" class="btn primary-btn">
                    <i class="fas fa-sign-in-alt"></i> Go to Login
                </a>
            </div>
        </div>
    </div>
</body>
</html> 