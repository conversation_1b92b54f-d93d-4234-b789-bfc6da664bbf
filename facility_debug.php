<?php
/**
 * Facility Names Debug Tool
 * 
 * This script helps troubleshoot facility name issues by:
 * 1. Checking all facility names in the users table
 * 2. Checking all facility names in the patients table
 * 3. Identifying discrepancies in naming between tables
 */

session_start();
include 'config.php';

// Check if user is logged in and is admin or super_admin
if(!isset($_SESSION['user_id'])) {
    echo "<div style='padding: 20px; background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; border-radius: 5px;'>Access denied. Please <a href='login.php'>login</a> as an admin to run this script.</div>";
    exit();
} else {
    // Check if user is admin or super_admin
    $user_id = $_SESSION['user_id'];
    $check_admin = "SELECT role FROM users WHERE id = ?";
    $stmt = $conn->prepare($check_admin);
    $stmt->bind_param("i", $user_id);
    $stmt->execute();
    $result = $stmt->get_result();
    $current_user = $result->fetch_assoc();
    
    if($current_user['role'] !== 'admin' && $current_user['role'] !== 'super_admin') {
        echo "<div style='padding: 20px; background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; border-radius: 5px;'>Access denied. Only admins can run this script.</div>";
        exit();
    }
}

// Function to get standardized form of facility name
function standardizeFacilityName($name) {
    // Convert to lowercase for comparison
    $name = strtolower($name);
    
    // Remove "health center" if present
    $name = str_replace("health center", "", $name);
    
    // Remove extra spaces
    $name = trim($name);
    
    return $name;
}

// Get all facility names from users table
$sql = "SELECT DISTINCT barangay FROM users ORDER BY barangay";
$result = $conn->query($sql);

$user_facilities = [];
if ($result->num_rows > 0) {
    while($row = $result->fetch_assoc()) {
        $user_facilities[] = $row['barangay'];
    }
}

// Get all facility names from patients table
$sql = "SELECT DISTINCT barangay FROM patients ORDER BY barangay";
$result = $conn->query($sql);

$patient_facilities = [];
if ($result->num_rows > 0) {
    while($row = $result->fetch_assoc()) {
        $patient_facilities[] = $row['barangay'];
    }
}

// Get patient counts by facility
$facility_counts = [];
foreach ($patient_facilities as $facility) {
    $sql = "SELECT COUNT(*) as count FROM patients WHERE barangay = ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("s", $facility);
    $stmt->execute();
    $result = $stmt->get_result();
    $data = $result->fetch_assoc();
    $facility_counts[$facility] = $data['count'];
}

// Find facility names that are in one table but not the other
$only_in_users = array_diff($user_facilities, $patient_facilities);
$only_in_patients = array_diff($patient_facilities, $user_facilities);

// Find facility names that might be the same but with different capitalization or wording
$possible_matches = [];
foreach ($only_in_users as $user_facility) {
    $std_user = standardizeFacilityName($user_facility);
    
    foreach ($only_in_patients as $patient_facility) {
        $std_patient = standardizeFacilityName($patient_facility);
        
        if ($std_user == $std_patient) {
            $possible_matches[] = [
                'user_version' => $user_facility,
                'patient_version' => $patient_facility,
                'patient_count' => $facility_counts[$patient_facility]
            ];
        }
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Facility Names Debug Tool</title>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Poppins', sans-serif;
            padding: 20px;
            max-width: 1200px;
            margin: 0 auto;
            line-height: 1.6;
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border-radius: 5px;
            background-color: #f8f9fa;
        }
        h1 {
            color: #2e7d32;
        }
        h2 {
            margin-top: 0;
            color: #2e7d32;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }
        th, td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background-color: #e8f5e9;
        }
        tr:hover {
            background-color: #f5f5f5;
        }
        .warning {
            background-color: #fff3e0;
        }
        .action-btn {
            display: inline-block;
            padding: 6px 12px;
            background: #2e7d32;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            font-size: 14px;
            margin-top: 10px;
        }
        .back-btn {
            display: inline-block;
            padding: 6px 12px;
            background: #607d8b;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            font-size: 14px;
            margin-top: 20px;
        }
        .standardize-btn {
            display: inline-block;
            padding: 6px 12px;
            background: #ff9800;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            font-size: 14px;
            margin-top: 20px;
            margin-left: 10px;
        }
    </style>
</head>
<body>
    <h1>Facility Names Debug Tool</h1>
    
    <div class="section">
        <h2>Facility Names in Users Table</h2>
        <p>These are all the facility names assigned to users:</p>
        <table>
            <tr>
                <th>Facility Name</th>
            </tr>
            <?php foreach ($user_facilities as $facility): ?>
            <tr>
                <td><?php echo htmlspecialchars($facility); ?></td>
            </tr>
            <?php endforeach; ?>
        </table>
    </div>
    
    <div class="section">
        <h2>Facility Names in Patients Table</h2>
        <p>These are all the facility names assigned to patients:</p>
        <table>
            <tr>
                <th>Facility Name</th>
                <th>Patient Count</th>
            </tr>
            <?php foreach ($patient_facilities as $facility): ?>
            <tr>
                <td><?php echo htmlspecialchars($facility); ?></td>
                <td><?php echo $facility_counts[$facility]; ?></td>
            </tr>
            <?php endforeach; ?>
        </table>
    </div>
    
    <?php if (!empty($possible_matches)): ?>
    <div class="section warning">
        <h2>Possible Facility Name Mismatches</h2>
        <p>These facility names might be the same but have different spellings or capitalization:</p>
        <table>
            <tr>
                <th>User Table Version</th>
                <th>Patient Table Version</th>
                <th>Patient Count</th>
            </tr>
            <?php foreach ($possible_matches as $match): ?>
            <tr>
                <td><?php echo htmlspecialchars($match['user_version']); ?></td>
                <td><?php echo htmlspecialchars($match['patient_version']); ?></td>
                <td><?php echo $match['patient_count']; ?></td>
            </tr>
            <?php endforeach; ?>
        </table>
        <p><strong>Note:</strong> These naming differences could prevent users from accessing patient records.</p>
    </div>
    <?php endif; ?>
    
    <a href="dashboard.php" class="back-btn">Back to Dashboard</a>
    <a href="standardize_patient_facilities.php" class="standardize-btn">Standardize Facility Names</a>
</body>
</html> 