<?php
include 'config.php';

// Display current table structure
echo "<h2>Current Users Table Structure:</h2>";
$show_table = "DESCRIBE users";
$result = $conn->query($show_table);
echo "<table border='1'>";
echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
while($row = $result->fetch_assoc()) {
    echo "<tr>";
    echo "<td>".$row['Field']."</td>";
    echo "<td>".$row['Type']."</td>";
    echo "<td>".$row['Null']."</td>";
    echo "<td>".$row['Key']."</td>";
    echo "<td>".$row['Default']."</td>";
    echo "<td>".$row['Extra']."</td>";
    echo "</tr>";
}
echo "</table>";

// Check if barangay column exists
$check_column = "SHOW COLUMNS FROM users LIKE 'barangay'";
$result = $conn->query($check_column);

if ($result->num_rows == 0) {
    echo "<h2>Adding Barangay Column:</h2>";
    $sql = "ALTER TABLE users ADD COLUMN barangay VARCHAR(100) DEFAULT NULL";
    if ($conn->query($sql) === TRUE) {
        echo "<p style='color: green;'>Column 'barangay' added successfully to users table.</p>";
    } else {
        echo "<p style='color: red;'>Error adding column: " . $conn->error . "</p>";
    }
} else {
    echo "<h2>Barangay Column Status:</h2>";
    echo "<p style='color: blue;'>Column 'barangay' already exists in users table.</p>";
}

// Display sample user data
echo "<h2>Sample User Data:</h2>";
$show_users = "SELECT id, username, barangay FROM users LIMIT 5";
$result = $conn->query($show_users);
echo "<table border='1'>";
echo "<tr><th>ID</th><th>Username</th><th>Barangay</th></tr>";
while($row = $result->fetch_assoc()) {
    echo "<tr>";
    echo "<td>".$row['id']."</td>";
    echo "<td>".$row['username']."</td>";
    echo "<td>".($row['barangay'] ?? 'NULL')."</td>";
    echo "</tr>";
}
echo "</table>";

// Create user_activity table
$sql = "CREATE TABLE IF NOT EXISTS user_activity (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    user_id INT(11) NOT NULL,
    action VARCHAR(255) NOT NULL,
    details TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
)";
if ($conn->query($sql) === TRUE) {
    echo "Table user_activity created successfully<br>";
} else {
    echo "Error creating table: " . $conn->error . "<br>";
}

// Add designation column if it doesn't exist
$check_designation = "SHOW COLUMNS FROM users LIKE 'designation'";
$result = $conn->query($check_designation);

if ($result->num_rows == 0) {
    $sql = "ALTER TABLE users ADD COLUMN designation VARCHAR(100) AFTER username";
    if ($conn->query($sql) === TRUE) {
        echo "<p style='color: green;'>Column 'designation' added successfully to users table.</p>";
    } else {
        echo "<p style='color: red;'>Error adding column: " . $conn->error . "</p>";
    }
} else {
    echo "<p style='color: blue;'>Column 'designation' already exists in users table.</p>";
}

// Check if email column exists and drop it if it does
$check_email = "SHOW COLUMNS FROM users LIKE 'email'";
$result = $conn->query($check_email);

if ($result->num_rows > 0) {
    $sql = "ALTER TABLE users DROP COLUMN email";
    if ($conn->query($sql) === TRUE) {
        echo "<p style='color: green;'>Column 'email' dropped successfully from users table.</p>";
    } else {
        echo "<p style='color: red;'>Error dropping column: " . $conn->error . "</p>";
    }
} else {
    echo "<p style='color: blue;'>Column 'email' does not exist in users table.</p>";
}

// Update status column in users table
$sql = "ALTER TABLE users 
        MODIFY COLUMN status ENUM('pending', 'active', 'inactive') DEFAULT 'pending'";

if ($conn->query($sql) === TRUE) {
    echo "Status column updated successfully<br>";
} else {
    echo "Error updating status column: " . $conn->error . "<br>";
}

// Create rejected_users table if it doesn't exist
$sql = "CREATE TABLE IF NOT EXISTS rejected_users (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL,
    fullname VARCHAR(100) DEFAULT NULL,
    barangay VARCHAR(50) DEFAULT NULL,
    designation VARCHAR(100) DEFAULT NULL,
    rejection_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    rejected_by INT(11) DEFAULT NULL
)";

if ($conn->query($sql) === TRUE) {
    echo "Rejected users table created successfully<br>";
} else {
    echo "Error creating rejected users table: " . $conn->error . "<br>";
}

$conn->close();
?> 