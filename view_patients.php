<?php
session_start();
include 'config.php';
include 'facility_access_handler.php';

// Check if user is logged in
if(!isset($_SESSION['user_id'])) {
    header("Location: login.php");
    exit();
}

// Get current user information
$user_id = $_SESSION['user_id'];
$check_user = "SELECT * FROM users WHERE id = ?";
$stmt = $conn->prepare($check_user);
$stmt->bind_param("i", $user_id);
$stmt->execute();
$result = $stmt->get_result();
$current_user = $result->fetch_assoc();

$username = $_SESSION['username'];

// Search functionality
$search = isset($_GET['search']) ? $_GET['search'] : '';
$where = '';

// Facility/Barangay based filtering
if ($current_user['role'] === 'super_admin') {
    // Super admins can see all patients
    if (!empty($search)) {
        $search = $conn->real_escape_string($search);
        $where = "WHERE (fullname LIKE '%$search%' OR control_number LIKE '%$search%' OR address LIKE '%$search%')";
    }
} else {
    // Regular users can only see patients from their facility
    // Always get the latest user data to ensure access rights are current
    $refresh_user = "SELECT barangay FROM users WHERE id = ?";
    $stmt = $conn->prepare($refresh_user);
    $stmt->bind_param("i", $user_id);
    $stmt->execute();
    $fresh_result = $stmt->get_result();
    $fresh_user = $fresh_result->fetch_assoc();

    // Use the most up-to-date facility information
    $user_barangay = $fresh_user['barangay'];

    if (!empty($search)) {
        $search = $conn->real_escape_string($search);
        $where = "WHERE barangay = '$user_barangay' AND (fullname LIKE '%$search%' OR control_number LIKE '%$search%' OR address LIKE '%$search%')";
    } else {
        $where = "WHERE barangay = '$user_barangay'";
    }
}

// Get filtered patients
$sql = "SELECT * FROM patients $where ORDER BY created_at DESC";
$result = $conn->query($sql);
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>View Patients</title>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Poppins', sans-serif;
        }

        body {
            font-family: 'Poppins', sans-serif;
            background: linear-gradient(135deg, #e8f5e9 0%, #c8e6c9 100%);
            margin: 0;
            padding: 0;
            min-height: 100vh;
        }

        /* Topbar Styles */
        .topbar {
            background: white;
            padding: 12px 20px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            display: flex;
            justify-content: space-between;
            align-items: center;
            height: 64px;
        }

        .logo-section {
            display: flex;
            align-items: center;
            height: 100%;
        }

        .logo {
            color: #2e7d32;
            font-size: 20px;
            font-weight: 600;
            display: flex;
            align-items: center;
            text-decoration: none;
            height: 100%;
            padding-right: 20px;
            margin-right: 20px;
            border-right: 1px solid #f0f0f0;
        }

        .logo-image {
            height: 40px;
            width: auto;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 3px;
        }

        .logo-text {
            display: flex;
            flex-direction: column;
        }

        .logo-title {
            color: #2e7d32;
            font-size: 18px;
            font-weight: 600;
            line-height: 1.1;
        }

        .logo-subtitle {
            color: #47a84e;
            font-size: 12px;
            font-weight: 500;
        }

        .nav-menu {
            display: flex;
            align-items: center;
            height: 100%;
        }

        .nav-item {
            display: flex;
            align-items: center;
            padding: 0 15px;
            color: #666;
            text-decoration: none;
            border-radius: 6px;
            transition: all 0.2s ease;
            font-size: 14px;
            height: 40px;
        }

        .nav-item:hover {
            background: #f0f7f0;
            color: #2e7d32;
        }

        .nav-item.active {
            background: #e8f5e9;
            color: #2e7d32;
            font-weight: 500;
        }

        .nav-item i {
            margin-right: 8px;
            width: 16px;
            text-align: center;
            font-size: 14px;
        }

        /* Main Content Styles */
        .main-content {
            margin-top: 60px;
            padding: 20px;
            width: 100%;
            box-sizing: border-box;
        }

        .user-profile {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .user-profile span {
            padding: 6px 12px;
            background: white;
            border-radius: 4px;
            color: #2e7d32;
            font-size: 13px;
            font-weight: 500;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
        }

        .logout-btn {
            padding: 6px 12px;
            background: #ffebee;
            border-radius: 4px;
            color: #dc3545;
            text-decoration: none;
            font-size: 13px;
            font-weight: 500;
            transition: all 0.2s ease;
        }

        .logout-btn:hover {
            background: #dc3545;
            color: white;
        }

        /* Table Styles */
        .table-container {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
            margin-top: 20px;
            border: 1px solid rgba(0, 0, 0, 0.05);
        }

        .patients-table {
            width: 100%;
            border-collapse: separate;
            border-spacing: 0;
        }

        .patients-table th {
            background: #f8faf8;
            color: #2e7d32;
            font-weight: 500;
            text-align: left;
            padding: 12px 15px;
            font-size: 13px;
            border-bottom: 1px solid #eee;
        }

        .patients-table td {
            padding: 12px 15px;
            font-size: 13px;
            color: #444;
            border-bottom: 1px solid #f0f0f0;
        }

        .patients-table tr:last-child td {
            border-bottom: none;
        }

        .patients-table tr:hover {
            background: #f8faf8;
        }

        .action-btn {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            text-decoration: none;
            margin-right: 5px;
            transition: all 0.2s ease;
        }

        .edit-btn {
            background: #e3f2fd;
            color: #1976d2;
        }

        .delete-btn {
            background: #ffebee;
            color: #dc3545;
        }

        .edit-btn:hover {
            background: #1976d2;
            color: white;
        }

        .delete-btn:hover {
            background: #dc3545;
            color: white;
        }

        /* Search Box Styles */
        .search-box {
            margin-bottom: 20px;
            display: flex;
            gap: 10px;
        }

        .search-input {
            flex: 1;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 8px;
            font-size: 15px;
            transition: all 0.3s ease;
        }

        .search-input:focus {
            outline: none;
            border-color: #20B2AA;
            box-shadow: 0 0 0 3px rgba(32,178,170,0.1);
        }

        .search-btn {
            padding: 12px 25px;
            background-color: #20B2AA;
            color: white;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .search-btn:hover {
            background-color: #1a9090;
        }

        .no-records {
            text-align: center;
            padding: 30px;
            color: #666;
            font-size: 16px;
        }

        .uppercase-text {
            text-transform: uppercase;
            font-weight: 500;
        }
    </style>
</head>
<body>
    <!-- Topbar -->
    <div class="topbar">
        <div class="logo-section">

            <nav class="nav-menu">
                <a href="dashboard.php" class="nav-item">
                    <i class="fas fa-home"></i>
                    Dashboard
                </a>
                <?php if($current_user['role'] === 'super_admin' || $current_user['role'] === 'admin' || $current_user['role'] === 'staff'): ?>
                <a href="add_patient.php" class="nav-item">
                    <i class="fas fa-user-plus"></i>
                    Add Patient
                </a>
                <?php endif; ?>
                <a href="view_patients.php" class="nav-item active">
                    <i class="fas fa-users"></i>
                    Patient Records
                </a>
                <?php if($current_user['role'] !== 'staff'): ?>
                <a href="manage_users.php" class="nav-item">
                    <i class="fas fa-users-cog"></i>
                    User Management
                </a>
                <?php endif; ?>
                <a href="logout.php" class="nav-item">
                    <i class="fas fa-sign-out-alt"></i>
                    Logout
                </a>
            </nav>
        </div>
        <div class="user-profile">
            <span><?php echo htmlspecialchars($username); ?></span>
        </div>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <div class="table-container">
            <h2 class="table-title">Patient Records</h2>
            <!-- Search Box -->
            <form method="GET" class="search-box">
                <input type="text" name="search" class="search-input" placeholder="Search by name, control number, or address..." value="<?php echo htmlspecialchars($search); ?>">
                <button type="submit" class="search-btn">
                    <i class="fas fa-search"></i>
                    Search
                </button>
            </form>

            <!-- Patients Table -->
            <table class="patients-table">
                <thead>
                    <tr>
                        <th>Control Number</th>
                        <th>Full Name</th>
                        <th>Age</th>
                        <th>Sex</th>
                        <th>Address</th>
                        <th>Facility/Barangay</th>
                        <th>Diagnosis</th>
                        <th>Purpose</th>
                        <th>Date Registered</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php if ($result->num_rows > 0): ?>
                        <?php while($row = $result->fetch_assoc()): ?>
                            <tr>
                                <td class="uppercase-text"><?php echo htmlspecialchars(strtoupper($row['control_number'])); ?></td>
                                <td class="uppercase-text"><?php echo htmlspecialchars(strtoupper($row['fullname'])); ?></td>
                                <td><?php echo htmlspecialchars($row['age']); ?></td>
                                <td class="uppercase-text"><?php echo htmlspecialchars(strtoupper($row['sex'])); ?></td>
                                <td class="uppercase-text"><?php echo htmlspecialchars(strtoupper($row['address'])); ?></td>
                                <td class="uppercase-text"><?php echo htmlspecialchars(strtoupper($row['barangay'])); ?></td>
                                <td class="uppercase-text"><?php echo htmlspecialchars(strtoupper($row['diagnosis'])); ?></td>
                                <td class="uppercase-text"><?php echo htmlspecialchars(strtoupper($row['purpose'])); ?></td>
                                <td><?php
                                    if(isset($row['registration_date']) && !empty($row['registration_date'])) {
                                        echo date('M d, Y', strtotime($row['registration_date']));
                                    } else {
                                        echo date('M d, Y', strtotime($row['created_at']));
                                    }
                                ?></td>
                                <td>
                                    <a href="print_medical_certificate.php?id=<?php echo $row['id']; ?>" class="action-btn view-btn" target="_blank">
                                        <i class="fas fa-print"></i>
                                        Print Certificate
                                    </a>
                                    <a href="edit_patient.php?id=<?php echo $row['id']; ?>" class="action-btn edit-btn">Edit</a>
                                    <a href="delete_patient.php?id=<?php echo $row['id']; ?>" class="action-btn delete-btn" onclick="return confirm('Are you sure you want to delete this patient?')">Delete</a>
                                </td>
                            </tr>
                        <?php endwhile; ?>
                    <?php else: ?>
                        <tr>
                            <td colspan="10" class="no-records">No patient records found.</td>
                        </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
    </div>
</body>
</html>