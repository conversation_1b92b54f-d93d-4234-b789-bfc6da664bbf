<?php
session_start();
include 'config.php';

// Check if user is logged in
if(!isset($_SESSION['user_id'])) {
    header("Location: login.php");
    exit();
}

// Process form submission
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    $barangay = $_POST['barangay'];
    $user_id = $_SESSION['user_id'];

    // Update user's barangay
    $sql = "UPDATE users SET barangay = ? WHERE id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("si", $barangay, $user_id);
    
    if ($stmt->execute()) {
        header("Location: dashboard.php");
        exit();
    } else {
        $error = "Failed to update barangay. Please try again.";
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Select Barangay</title>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="style.css">
    <style>
        body {
            background: var(--bg-gradient);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: var(--card-gradient);
            padding: 40px;
            border-radius: var(--radius-md);
            box-shadow: var(--shadow-md);
        }

        .logo {
            background: var(--primary-gradient);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            font-size: var(--font-xl);
            font-weight: 600;
            margin-bottom: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }

        .form-title {
            color: var(--text-primary);
            font-size: var(--font-lg);
            margin-bottom: 30px;
            text-align: center;
        }

        .barangay-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 30px;
        }

        .barangay-btn {
            background: var(--card-gradient);
            border: 2px solid transparent;
            border-image: var(--primary-gradient);
            border-image-slice: 1;
            color: var(--text-primary);
            padding: 15px;
            border-radius: var(--radius-sm);
            font-size: var(--font-sm);
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
            box-shadow: var(--shadow-sm);
        }

        .barangay-btn:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-md);
            background: var(--card-hover-gradient);
        }

        .barangay-btn.selected {
            background: var(--primary-gradient);
            color: white;
            border: 2px solid transparent;
        }

        .submit-btn {
            background: var(--primary-gradient);
            color: white;
            padding: 12px;
            border: none;
            border-radius: var(--radius-sm);
            font-size: var(--font-md);
            font-weight: 500;
            cursor: pointer;
            width: 200px;
            margin: 0 auto;
            display: block;
            transition: all 0.3s ease;
            box-shadow: var(--shadow-sm);
        }

        .submit-btn:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-md);
        }

        .submit-btn:disabled {
            background: linear-gradient(135deg, #cccccc 0%, #aaaaaa 100%);
            cursor: not-allowed;
        }

        .error-message {
            background: var(--danger-gradient);
            color: white;
            padding: 15px;
            border-radius: var(--radius-sm);
            margin-bottom: 20px;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">
            <i class="fas fa-hospital"></i>
            Health System
        </div>
        
        <h2 class="form-title">Select Your Barangay</h2>

        <?php if(isset($error)): ?>
            <div class="error-message">
                <?php echo $error; ?>
            </div>
        <?php endif; ?>

        <form method="POST" action="" id="barangayForm">
            <input type="hidden" name="barangay" id="selectedBarangay">
            
            <div class="barangay-grid">
                <button type="button" class="barangay-btn" data-barangay="Baclaran">Baclaran</button>
                <button type="button" class="barangay-btn" data-barangay="B. F. Homes">B. F. Homes</button>
                <button type="button" class="barangay-btn" data-barangay="Don Bosco">Don Bosco</button>
                <button type="button" class="barangay-btn" data-barangay="Don Galo">Don Galo</button>
                <button type="button" class="barangay-btn" data-barangay="La Huerta">La Huerta</button>
                <button type="button" class="barangay-btn" data-barangay="Marcelo Green">Marcelo Green</button>
                <button type="button" class="barangay-btn" data-barangay="Merville">Merville</button>
                <button type="button" class="barangay-btn" data-barangay="Moonwalk">Moonwalk</button>
                <button type="button" class="barangay-btn" data-barangay="San Antonio">San Antonio</button>
                <button type="button" class="barangay-btn" data-barangay="San Dionisio">San Dionisio</button>
                <button type="button" class="barangay-btn" data-barangay="San Isidro">San Isidro</button>
                <button type="button" class="barangay-btn" data-barangay="San Martin de Porres">San Martin de Porres</button>
                <button type="button" class="barangay-btn" data-barangay="Sto. Niño">Sto. Niño</button>
                <button type="button" class="barangay-btn" data-barangay="Sun Valley">Sun Valley</button>
                <button type="button" class="barangay-btn" data-barangay="Tambo">Tambo</button>
                <button type="button" class="barangay-btn" data-barangay="Vitalez">Vitalez</button>
            </div>

            <button type="submit" class="submit-btn" disabled>Continue</button>
        </form>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const barangayButtons = document.querySelectorAll('.barangay-btn');
            const submitButton = document.querySelector('.submit-btn');
            const selectedBarangayInput = document.getElementById('selectedBarangay');
            
            barangayButtons.forEach(button => {
                button.addEventListener('click', function() {
                    // Remove selected class from all buttons
                    barangayButtons.forEach(btn => btn.classList.remove('selected'));
                    
                    // Add selected class to clicked button
                    this.classList.add('selected');
                    
                    // Set the selected barangay value
                    selectedBarangayInput.value = this.dataset.barangay;
                    
                    // Enable submit button
                    submitButton.disabled = false;
                });
            });
        });
    </script>
</body>
</html> 