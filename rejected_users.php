<?php
session_start();
include 'config.php';

// Check if user is logged in and is super admin
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'super_admin') {
    header("Location: login.php");
    exit();
}

// Get current user's information
$user_id = $_SESSION['user_id'];
$username = $_SESSION['username'];

// Get current user's role and fullname
$check_user_sql = "SELECT role, fullname FROM users WHERE id = ?";
$stmt = $conn->prepare($check_user_sql);
$stmt->bind_param("i", $user_id);
$stmt->execute();
$result = $stmt->get_result();
$current_user = $result->fetch_assoc();

// Count rejected users
$rejected_count_sql = "SELECT COUNT(*) as count FROM rejected_users";
$rejected_count_result = $conn->query($rejected_count_sql);
$rejected_count = $rejected_count_result->fetch_assoc()['count'];
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Rejected Users</title>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: 'Poppins', sans-serif;
            background: linear-gradient(135deg, #e8f5e9 0%, #c8e6c9 100%);
            margin: 0;
            padding: 0;
            min-height: 100vh;
        }

        /* Topbar Styles */
        .topbar {
            background: white;
            padding: 12px 20px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo-section {
            display: flex;
            align-items: center;
            gap: 40px;
        }

        .logo {
            color: #2e7d32;
            font-size: 20px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 8px;
            text-decoration: none;
        }

        .nav-menu {
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .nav-item {
            display: flex;
            align-items: center;
            padding: 8px 15px;
            color: #666;
            text-decoration: none;
            border-radius: 6px;
            transition: all 0.2s ease;
            font-size: 14px;
        }

        .nav-item:hover {
            background: #f0f7f0;
            color: #2e7d32;
        }

        .nav-item.active {
            background: #e8f5e9;
            color: #2e7d32;
            font-weight: 500;
        }

        .nav-item i {
            margin-right: 8px;
            width: 16px;
            text-align: center;
            font-size: 14px;
        }

        .user-profile {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .logout-btn {
            display: flex;
            align-items: center;
            gap: 5px;
            color: #d32f2f;
            text-decoration: none;
            font-size: 14px;
            padding: 5px 10px;
            border-radius: 4px;
            transition: all 0.2s ease;
        }

        .logout-btn:hover {
            background: #ffebee;
        }

        /* Main Content Styles */
        .main-content {
            margin-top: 60px;
            padding: 20px;
            width: 100%;
            box-sizing: border-box;
        }

        .table-container {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
            padding: 20px;
            margin-bottom: 20px;
        }

        .section-title {
            font-size: 18px;
            font-weight: 600;
            color: #2e7d32;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .users-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }

        .users-table th, .users-table td {
            padding: 12px 15px;
            border-bottom: 1px solid #eee;
            text-align: left;
        }

        .users-table th {
            font-weight: 500;
            color: #333;
            background: #f9f9f9;
        }

        .users-table tr:last-child td {
            border-bottom: none;
        }

        .users-table tr:hover {
            background: #f5f5f5;
        }

        .action-buttons {
            display: flex;
            gap: 5px;
        }

        .no-records-container {
            padding: 30px;
            text-align: center;
        }

        .no-records {
            text-align: center;
            color: #666;
            font-style: italic;
        }

        .back-button {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            background: #2e7d32;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            font-size: 14px;
            cursor: pointer;
            text-decoration: none;
            margin-bottom: 20px;
            transition: all 0.2s ease;
        }

        .back-button:hover {
            background: #1b5e20;
        }

        .rejected-count {
            background: #f44336;
            color: white;
            padding: 3px 8px;
            border-radius: 12px;
            font-size: 12px;
            margin-left: 8px;
        }

        .rejected-badge {
            display: inline-flex;
            align-items: center;
            background: #ffebee;
            color: #d32f2f;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            margin-left: 8px;
        }

        .rejected-badge i {
            font-size: 10px;
            margin-right: 4px;
        }
    </style>
</head>
<body>
    <!-- Topbar -->
    <div class="topbar">
        <div class="logo-section">
            <a href="dashboard.php" class="logo">
                <i class="fas fa-hospital"></i>
                MedCert
            </a>
            <nav class="nav-menu">
                <a href="dashboard.php" class="nav-item">
                    <i class="fas fa-home"></i>
                    Dashboard
                </a>
                <?php if($current_user['role'] === 'super_admin' || $current_user['role'] === 'admin' || $current_user['role'] === 'staff'): ?>
                <a href="add_patient.php" class="nav-item">
                    <i class="fas fa-user-plus"></i>
                    Add Patient
                </a>
                <?php endif; ?>
                <a href="view_patients.php" class="nav-item">
                    <i class="fas fa-users"></i>
                    Patient Records
                </a>
                <?php if($current_user['role'] !== 'staff'): ?>
                <a href="manage_users.php" class="nav-item active">
                    <i class="fas fa-users-cog"></i>
                    User Management
                </a>
                <a href="rejected_users.php" class="nav-item">
                    <i class="fas fa-user-times"></i>
                    Rejected Users
                </a>
                <?php endif; ?>
                <a href="logout.php" class="nav-item">
                    <i class="fas fa-sign-out-alt"></i>
                    Logout
                </a>
            </nav>
        </div>
        <div class="user-profile">
            <span><?php echo htmlspecialchars($username); ?></span>
        </div>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <a href="manage_users.php" class="back-button">
            <i class="fas fa-arrow-left"></i> Back to User Management
        </a>
        
        <div class="table-container">
            <div class="section-title">
                <i class="fas fa-user-times"></i>
                Rejected Users
                <?php if($rejected_count > 0): ?>
                <span class="rejected-badge"><?php echo $rejected_count; ?></span>
                <?php endif; ?>
            </div>
            
            <?php if($rejected_count > 0): ?>
            <table class="users-table">
                <thead>
                    <tr>
                        <th>Username</th>
                        <th>Full Name</th>
                        <th>Designation</th>
                        <th>Facility</th>
                        <th>Rejection Date</th>
                        <th>Rejected By</th>
                    </tr>
                </thead>
                <tbody>
                    <?php 
                    // Fetch rejected users
                    $rejected_sql = "SELECT r.*, u.username as rejected_by_user 
                                    FROM rejected_users r 
                                    LEFT JOIN users u ON r.rejected_by = u.id 
                                    ORDER BY r.rejection_date DESC";
                    $rejected_result = $conn->query($rejected_sql);

                    while($row = $rejected_result->fetch_assoc()) {
                        echo "<tr>";
                        echo "<td>" . htmlspecialchars($row['username']) . "</td>";
                        echo "<td>" . htmlspecialchars($row['fullname']) . "</td>";
                        echo "<td>" . htmlspecialchars($row['designation']) . "</td>";
                        echo "<td>" . htmlspecialchars($row['barangay']) . " Health Center</td>";
                        echo "<td>" . date('M d, Y g:i A', strtotime($row['rejection_date'])) . "</td>";
                        echo "<td>" . htmlspecialchars($row['rejected_by_user'] ?? 'Unknown') . "</td>";
                        echo "</tr>";
                    }
                    ?>
                </tbody>
            </table>
            <?php else: ?>
            <div class="no-records-container">
                <p class="no-records">No rejected users found</p>
            </div>
            <?php endif; ?>
        </div>
    </div>
</body>
</html> 