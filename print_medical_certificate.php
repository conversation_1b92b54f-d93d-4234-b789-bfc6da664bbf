<?php
session_start();
include 'config.php';
include 'activity_logger.php';

// Check if user is logged in
if(!isset($_SESSION['user_id'])) {
    header("Location: login.php");
    exit();
}

// Get patient ID from URL
$patient_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

// Get patient information
$sql = "SELECT * FROM patients WHERE id = ?";
$stmt = $conn->prepare($sql);
$stmt->bind_param("i", $patient_id);
$stmt->execute();
$result = $stmt->get_result();
$patient = $result->fetch_assoc();

if (!$patient) {
    header("Location: view_patients.php");
    exit();
}

// Log the activity
logActivity($conn, $_SESSION['user_id'], "Print Certificate", "Printed medical certificate for patient: " . $patient['fullname']);

// Set default values for fields that might not exist in the database
$diagnosis = isset($patient['purpose']) ? $patient['purpose'] : '___________________________';
$recommendation = 'Follow up after 3 days if symptoms persist';

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Medical Certificate</title>
    <style>
        @page {
            size: A4;
            margin: 1in;
        }
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 0;
            font-size: 12pt;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .header img {
            width: 80px;
            height: auto;
            margin-bottom: 10px;
        }
        .header-text {
            margin: 0;
            line-height: 1.3;
        }
        .header-text h3 {
            margin: 3px 0;
            font-size: 14pt;
            font-weight: normal;
        }
        .header-text p {
            margin: 3px 0;
            font-size: 11pt;
        }
        .title {
            text-align: center;
            font-size: 16pt;
            font-weight: bold;
            margin: 30px 0;
        }
        .content {
            margin: 20px 0;
        }
        .date-line {
            margin-bottom: 20px;
        }
        .underline {
            border-bottom: 1px solid black;
            display: inline-block;
            min-width: 200px;
            padding: 0 5px;
        }
        .diagnosis, .recommendation {
            margin: 15px 0;
            text-align: justify;
        }
        .signature-section {
            display: flex;
            justify-content: space-between;
            margin-top: 40px;
            margin-bottom: 25px;
            position: relative;
        }
        .doctor-info {
            width: 300px;
            text-align: left;
            margin-left: 0;
            line-height: 1.1;
        }
        .doctor-info p {
            margin: 0;
            padding: 0;
        }
        .doctor-name {
            font-weight: bold;
            margin-bottom: 0;
            font-size: 12pt;
        }
        .doctor-details {
            margin: 2px 0;
            font-size: 11pt;
        }
        .control-number {
            margin-top: 30px;
            font-size: 11pt;
        }
        .signature-line {
            border-bottom: 1px solid black;
            display: inline-block;
            width: 250px;
            padding: 0;
            margin-bottom: 1px;
        }
        .signature-label {
            font-size: 10pt;
            text-align: left;
            margin-top: 0;
            margin-bottom: 5px;
            color: #444;
        }
        .health-center {
            margin-top: 3px;
            font-weight: 500;
            font-size: 11pt;
            text-align: left;
            color: #333;
        }
        @media print {
            .no-print {
                display: none;
            }
        }
        .print-button {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 10px 20px;
            background-color: #20B2AA;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }
        .print-button:hover {
            background-color: #1a9090;
        }
        .main-content {
            width: 100%;
            max-width: 8.27in;
            margin: 0 auto;
            padding: 0;
        }
    </style>
</head>
<body>
    <button onclick="window.print()" class="print-button no-print">Print Certificate</button>

    <div class="main-content">
        <div class="header">
            <img src="logo.png" alt="Logo">
            <div class="header-text">
                <h3>REPUBLIC OF THE PHILIPPINES</h3>
                <h3>City Government of Parañaque</h3>
                <h3>CITY HEALTH OFFICE</h3>
                <h3>BF HOMES HEALTH CENTER</h3>
                <p>Conrada Benitez Street Corner Dela Rama BF Homes Parañaque City</p>
                <p>Tel No. 8826-6358 / 8255-7400 / Cell No. 0916-116-5887</p>
            </div>
        </div>

        <div class="title">
            MEDICAL CERTIFICATE
        </div>

        <div class="content">
            <div class="date-line">
                <span class="underline"><?php echo date('F d, Y'); ?></span>
                <br>Date
            </div>

            <p>This is to certify that Mr./Ms. 
                <span class="underline"><?php echo htmlspecialchars($patient['fullname']); ?></span>, 
                <span class="underline"><?php echo htmlspecialchars($patient['age']); ?></span> year/s old of
                <br><span class="underline"><?php echo htmlspecialchars($patient['address']); ?></span>, was seen and examined on 
                <span class="underline"><?php echo date('F d, Y'); ?></span>.
            </p>

            <div class="diagnosis">
                <p>Diagnosis: <span class="underline"><?php echo htmlspecialchars($diagnosis); ?></span></p>
            </div>
            
            <div class="recommendation">
                <p>Recommendation: <span class="underline"><?php echo htmlspecialchars($recommendation); ?></span></p>
            </div>

            <p>This certification is issued upon the request of the bearer for whatever purpose it may serve except for medico-legal.</p>
            
            <p>Thank you.</p>
        </div>

        <div class="signature-section">
            <div class="doctor-info">
                <span class="signature-line">&nbsp;</span>
                <p class="signature-label">Physician's Signature</p>
                
                <span class="signature-line">&nbsp;</span>
                <p class="signature-label">Position/Designation</p>
                
                <span class="signature-line">&nbsp;</span>
                <p class="signature-label">License Number</p>
                
                <p class="health-center">BF HOMES HEALTH CENTER</p>
            </div>
        </div>

        <div class="control-number">
            Control Number: <?php echo str_pad($patient['id'], 6, '0', STR_PAD_LEFT); ?>
        </div>
    </div>
</body>
</html> 