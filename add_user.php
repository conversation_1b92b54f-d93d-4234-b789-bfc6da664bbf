<?php
session_start();
include 'config.php';

// Check if user is logged in and is admin or super_admin
if(!isset($_SESSION['user_id'])) {
    header("Location: login.php");
    exit();
} else {
    // Check if user is admin or super_admin
    $user_id = $_SESSION['user_id'];
    $check_admin = "SELECT role FROM users WHERE id = ?";
    $stmt = $conn->prepare($check_admin);
    $stmt->bind_param("i", $user_id);
    $stmt->execute();
    $result = $stmt->get_result();
    $current_user = $result->fetch_assoc();
    
    if($current_user['role'] !== 'admin' && $current_user['role'] !== 'super_admin') {
        header("Location: dashboard.php");
        exit();
    }
}

// Get current user's username for display
$current_user_username = $_SESSION['username'];

// Handle form submission
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    $username = $conn->real_escape_string($_POST['username']);
    $password = $_POST['password'];
    $confirm_password = $_POST['confirm_password'];
    $fullname = $conn->real_escape_string($_POST['fullname']);
    $designation = $conn->real_escape_string($_POST['designation']);
    $role = $conn->real_escape_string($_POST['role']);
    $barangay = $conn->real_escape_string($_POST['barangay']);
    
    // Only super_admin can create another super_admin
    if($role === 'super_admin' && $current_user['role'] !== 'super_admin') {
        $error_message = "You don't have permission to create a super admin account.";
    } else {
        // Validate password match
        if($password !== $confirm_password) {
            $error_message = "Passwords do not match!";
        } else {
            // Check if username already exists
            $check_username = "SELECT id FROM users WHERE username = ?";
            $stmt = $conn->prepare($check_username);
            $stmt->bind_param("s", $username);
            $stmt->execute();
            $result = $stmt->get_result();
            
            if($result->num_rows > 0) {
                $error_message = "Username already exists!";
            } else {
                // Hash password
                $hashed_password = password_hash($password, PASSWORD_DEFAULT);
                
                // Insert new user
                $sql = "INSERT INTO users (username, password, fullname, designation, role, status, barangay) VALUES (?, ?, ?, ?, ?, 'active', ?)";
                $stmt = $conn->prepare($sql);
                $stmt->bind_param("ssssss", $username, $hashed_password, $fullname, $designation, $role, $barangay);
                
                if($stmt->execute()) {
                    $_SESSION['success'] = "User added successfully!";
                    header("Location: manage_users.php");
                    exit();
                } else {
                    $error_message = "Error adding user: " . $stmt->error;
                }
            }
        }
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Add New User</title>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Poppins', sans-serif;
        }

        body {
            background-color: #f5f7fb;
            display: flex;
        }

        /* Sidebar Styles */
        .sidebar {
            width: 250px;
            background-color: white;
            height: 100vh;
            padding: 20px;
            position: fixed;
            box-shadow: 2px 0 5px rgba(0,0,0,0.05);
        }

        .logo {
            color: #20B2AA;
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 40px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .nav-item {
            display: flex;
            align-items: center;
            padding: 12px 15px;
            color: #666;
            text-decoration: none;
            border-radius: 8px;
            margin-bottom: 5px;
            transition: all 0.3s ease;
        }

        .nav-item:hover, .nav-item.active {
            background-color: #20B2AA;
            color: white;
        }

        .nav-item i {
            margin-right: 10px;
            width: 20px;
            text-align: center;
        }

        /* Main Content Styles */
        .main-content {
            margin-left: 250px;
            padding: 20px;
            width: calc(100% - 250px);
        }

        /* Header Styles */
        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            background: white;
            padding: 15px 20px;
            border-radius: 10px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
        }

        .page-title {
            color: #333;
            font-size: 24px;
        }

        .user-profile {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .user-profile img {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            object-fit: cover;
        }

        /* Form Styles */
        .form-container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
            max-width: 600px;
            margin: 0 auto;
        }

        .form-title {
            color: #333;
            margin-bottom: 30px;
            text-align: center;
            font-size: 20px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #333;
            font-weight: 500;
        }

        .form-group input,
        .form-group select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 8px;
            font-size: 15px;
            transition: all 0.3s ease;
        }

        .form-group input:focus,
        .form-group select:focus {
            outline: none;
            border-color: #20B2AA;
            box-shadow: 0 0 0 3px rgba(32,178,170,0.1);
        }

        .submit-btn {
            background-color: #20B2AA;
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            width: 100%;
            transition: all 0.3s ease;
        }

        .submit-btn:hover {
            background-color: #1a9090;
        }

        .error-message {
            background-color: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }

        .back-btn {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            color: #20B2AA;
            text-decoration: none;
            margin-bottom: 20px;
            font-weight: 500;
        }

        .back-btn:hover {
            color: #1a9090;
        }
    </style>
</head>
<body>
    <!-- Sidebar -->
    <div class="sidebar">
        <div class="logo">
            <i class="fas fa-hospital"></i>
            Alagang Parañaque
        </div>
        <nav>
            <a href="dashboard.php" class="nav-item">
                <i class="fas fa-home"></i>
                Dashboard
            </a>
            <a href="register_patient.php" class="nav-item">
                <i class="fas fa-user-plus"></i>
                Register Patient
            </a>
            <a href="view_patients.php" class="nav-item">
                <i class="fas fa-users"></i>
                Patient Records
            </a>
            <?php if($current_user['role'] !== 'staff'): ?>
            <a href="manage_users.php" class="nav-item active">
                <i class="fas fa-users-cog"></i>
                User Management
            </a>
            <?php endif; ?>
            <a href="logout.php" class="nav-item">
                <i class="fas fa-sign-out-alt"></i>
                Logout
            </a>
        </nav>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Header -->
        <div class="header">
            <h1 class="page-title">Add New User</h1>
            <div class="user-profile">
                <span><?php echo htmlspecialchars($current_user_username); ?></span>
                <img src="https://via.placeholder.com/40" alt="User Profile">
            </div>
        </div>

        <!-- Back Button -->
        <a href="manage_users.php" class="back-btn">
            <i class="fas fa-arrow-left"></i>
            Back to User Management
        </a>

        <!-- Add User Form -->
        <div class="form-container">
            <?php if(isset($error_message)): ?>
                <div class="error-message"><?php echo $error_message; ?></div>
            <?php endif; ?>

            <form method="POST" action="">
                <div class="form-group">
                    <label for="username">Username</label>
                    <input type="text" id="username" name="username" required>
                </div>

                <div class="form-group">
                    <label for="password">Password</label>
                    <input type="password" id="password" name="password" required>
                </div>

                <div class="form-group">
                    <label for="confirm_password">Confirm Password</label>
                    <input type="password" id="confirm_password" name="confirm_password" required>
                </div>

                <div class="form-group">
                    <label for="fullname">Full Name</label>
                    <input type="text" id="fullname" name="fullname" required>
                </div>

                <div class="form-group">
                    <label for="designation">Designation</label>
                    <input type="text" id="designation" name="designation" required>
                </div>

                <div class="form-group">
                    <label for="role">Role</label>
                    <select id="role" name="role" required>
                        <?php if($current_user['role'] === 'super_admin'): ?>
                            <option value="super_admin">Super Admin</option>
                        <?php endif; ?>
                        <option value="admin">Admin</option>
                        <option value="staff">Staff</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="barangay">Facility</label>
                    <select id="barangay" name="barangay" required>
                        <option value="">Select Facility</option>
                        <option value="Baclaran Health Center">Baclaran Health Center</option>
                        <option value="BF Homes Health Center">BF Homes Health Center</option>
                        <option value="Don Bosco Health Center">Don Bosco Health Center</option>
                        <option value="Don Galo Health Center">Don Galo Health Center</option>
                        <option value="La Huerta Health Center">La Huerta Health Center</option>
                        <option value="Marcelo Green Health Center">Marcelo Green Health Center</option>
                        <option value="Merville Health Center">Merville Health Center</option>
                        <option value="Moonwalk Health Center">Moonwalk Health Center</option>
                        <option value="San Antonio Health Center">San Antonio Health Center</option>
                        <option value="San Dionisio Health Center">San Dionisio Health Center</option>
                        <option value="San Isidro Health Center">San Isidro Health Center</option>
                        <option value="San Martin De Porres Health Center">San Martin De Porres Health Center</option>
                        <option value="Santo Niño Health Center">Santo Niño Health Center</option>
                        <option value="Sun Valley Health Center">Sun Valley Health Center</option>
                        <option value="Tambo Health Center">Tambo Health Center</option>
                        <option value="Vitalez Health Center">Vitalez Health Center</option>
                    </select>
                </div>

                <button type="submit" class="submit-btn">Add User</button>
            </form>
        </div>
    </div>
</body>
</html> 