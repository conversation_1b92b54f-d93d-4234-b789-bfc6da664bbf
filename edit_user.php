<?php
session_start();
include 'config.php';
include 'activity_logger.php';
include 'facility_access_handler.php';

// Check if user is logged in and is admin or super_admin
if(!isset($_SESSION['user_id'])) {
    header("Location: login.php");
    exit();
} else {
    // Check if user is admin or super_admin
    $user_id = $_SESSION['user_id'];
    $check_admin = "SELECT role FROM users WHERE id = ?";
    $stmt = $conn->prepare($check_admin);
    $stmt->bind_param("i", $user_id);
    $stmt->execute();
    $result = $stmt->get_result();
    $current_user = $result->fetch_assoc();

    if($current_user['role'] !== 'admin' && $current_user['role'] !== 'super_admin') {
        header("Location: dashboard.php");
        exit();
    }
}

// Get user ID from URL
if(!isset($_GET['id'])) {
    header("Location: manage_users.php");
    exit();
}

$edit_user_id = $_GET['id'];

// Get user information
$sql = "SELECT * FROM users WHERE id = ?";
$stmt = $conn->prepare($sql);
$stmt->bind_param("i", $edit_user_id);
$stmt->execute();
$result = $stmt->get_result();
$user = $result->fetch_assoc();

if (!$user) {
    header("Location: manage_users.php");
    exit();
}

// Get current user's username for display
$current_user_username = $_SESSION['username'];

// Handle form submission
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    $new_username = $conn->real_escape_string($_POST['username']);
    $new_fullname = $conn->real_escape_string($_POST['fullname']);
    $new_designation = $conn->real_escape_string($_POST['designation']);
    $new_role = $conn->real_escape_string($_POST['role']);
    $new_status = $conn->real_escape_string($_POST['status']);
    $new_password = $_POST['password'];
    $new_barangay = $conn->real_escape_string($_POST['barangay']);

    // Track if facility has changed
    $facility_changed = ($user['barangay'] !== $new_barangay);
    $old_facility = $user['barangay'];

    // Check if username is being changed and if it already exists
    if($new_username !== $user['username']) {
        $check_username = "SELECT id FROM users WHERE username = ? AND id != ?";
        $stmt = $conn->prepare($check_username);
        $stmt->bind_param("si", $new_username, $edit_user_id);
        $stmt->execute();
        $result = $stmt->get_result();

        if ($result->num_rows > 0) {
            $error_message = "Username already exists!";
        } else {
            // Update user information including username
            if(!empty($new_password)) {
                $hashed_password = password_hash($new_password, PASSWORD_DEFAULT);
                $sql = "UPDATE users SET username = ?, fullname = ?, designation = ?, role = ?, status = ?, password = ?, barangay = ? WHERE id = ?";
                $stmt = $conn->prepare($sql);
                $stmt->bind_param("sssssssi", $new_username, $new_fullname, $new_designation, $new_role, $new_status, $hashed_password, $new_barangay, $edit_user_id);
            } else {
                $sql = "UPDATE users SET username = ?, fullname = ?, designation = ?, role = ?, status = ?, barangay = ? WHERE id = ?";
                $stmt = $conn->prepare($sql);
                $stmt->bind_param("ssssssi", $new_username, $new_fullname, $new_designation, $new_role, $new_status, $new_barangay, $edit_user_id);
            }

            if ($stmt->execute()) {
                $details = "Updated user: " . $new_fullname . " (ID: " . $edit_user_id . ")";
                logActivity($conn, $_SESSION['user_id'], "Edit User", $details);

                // Handle facility change if applicable
                if ($facility_changed) {
                    $facility_change_details = "Changed facility from '$old_facility' to '$new_barangay' for user: " . $new_fullname . " (ID: " . $edit_user_id . ")";
                    logActivity($conn, $_SESSION['user_id'], "Facility Change", $facility_change_details);

                    // Update access rights for the user
                    handleFacilityChange($conn, $edit_user_id, $old_facility, $new_barangay);

                    // Calculate number of accessible patients
                    $patient_count = countPatientsInFacility($conn, $new_barangay);

                    // For debugging, log the count and query details
                    logActivity($conn, $_SESSION['user_id'], "Facility Access Debug",
                        "Patient count for $new_barangay: $patient_count. Old facility: $old_facility");

                    if ($patient_count > 0) {
                        $_SESSION['success'] = "User updated successfully! User now has access to " . $patient_count . " patient record" . ($patient_count > 1 ? "s" : "") . " in " . $new_barangay . ".";
                    } else {
                        $_SESSION['success'] = "User updated successfully! Facility changed to " . $new_barangay . ". There are currently no patient records in this facility.";
                    }
                } else {
                $_SESSION['success'] = "User updated successfully!";
                }

                header("Location: manage_users.php");
                exit();
            } else {
                $error_message = "Error updating user: " . $stmt->error;
            }
        }
    } else {
        // Update user information without changing username
        if(!empty($new_password)) {
            $hashed_password = password_hash($new_password, PASSWORD_DEFAULT);
            $sql = "UPDATE users SET fullname = ?, designation = ?, role = ?, status = ?, password = ?, barangay = ? WHERE id = ?";
            $stmt = $conn->prepare($sql);
            $stmt->bind_param("ssssssi", $new_fullname, $new_designation, $new_role, $new_status, $hashed_password, $new_barangay, $edit_user_id);
        } else {
            $sql = "UPDATE users SET fullname = ?, designation = ?, role = ?, status = ?, barangay = ? WHERE id = ?";
            $stmt = $conn->prepare($sql);
            $stmt->bind_param("sssssi", $new_fullname, $new_designation, $new_role, $new_status, $new_barangay, $edit_user_id);
        }

        if ($stmt->execute()) {
            $details = "Updated user: " . $new_fullname . " (ID: " . $edit_user_id . ")";
            logActivity($conn, $_SESSION['user_id'], "Edit User", $details);

            // Handle facility change if applicable
            if ($facility_changed) {
                $facility_change_details = "Changed facility from '$old_facility' to '$new_barangay' for user: " . $new_fullname . " (ID: " . $edit_user_id . ")";
                logActivity($conn, $_SESSION['user_id'], "Facility Change", $facility_change_details);

                // Update access rights for the user
                handleFacilityChange($conn, $edit_user_id, $old_facility, $new_barangay);

                // Calculate number of accessible patients
                $patient_count = countPatientsInFacility($conn, $new_barangay);

                // For debugging, log the count and query details
                logActivity($conn, $_SESSION['user_id'], "Facility Access Debug",
                    "Patient count for $new_barangay: $patient_count. Old facility: $old_facility");

                if ($patient_count > 0) {
                    $_SESSION['success'] = "User updated successfully! User now has access to " . $patient_count . " patient record" . ($patient_count > 1 ? "s" : "") . " in " . $new_barangay . ".";
                } else {
                    $_SESSION['success'] = "User updated successfully! Facility changed to " . $new_barangay . ". There are currently no patient records in this facility.";
                }
            } else {
            $_SESSION['success'] = "User updated successfully!";
            }

            header("Location: manage_users.php");
            exit();
        } else {
            $error_message = "Error updating user: " . $stmt->error;
        }
    }

    $stmt->close();
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Edit User</title>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: 'Poppins', sans-serif;
            background: linear-gradient(135deg, #e8f5e9 0%, #c8e6c9 100%);
            margin: 0;
            padding: 0;
            min-height: 100vh;
        }

        /* Topbar Styles */
        .topbar {
            background: white;
            padding: 12px 20px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            display: flex;
            justify-content: space-between;
            align-items: center;
            height: 64px;
        }

        .logo-section {
            display: flex;
            align-items: center;
            height: 100%;
        }

        .logo {
            color: #2e7d32;
            font-size: 20px;
            font-weight: 600;
            display: flex;
            align-items: center;
            text-decoration: none;
            height: 100%;
            padding-right: 20px;
            margin-right: 20px;
            border-right: 1px solid #f0f0f0;
        }

        .logo-image {
            height: 40px;
            width: auto;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 3px;
        }

        .logo-text {
            display: flex;
            flex-direction: column;
        }

        .logo-title {
            color: #2e7d32;
            font-size: 18px;
            font-weight: 600;
            line-height: 1.1;
        }

        .logo-subtitle {
            color: #47a84e;
            font-size: 12px;
            font-weight: 500;
        }

        .nav-menu {
            display: flex;
            align-items: center;
            height: 100%;
        }

        .nav-item {
            display: flex;
            align-items: center;
            padding: 0 15px;
            color: #666;
            text-decoration: none;
            border-radius: 6px;
            transition: all 0.2s ease;
            font-size: 14px;
            height: 40px;
        }

        .nav-item:hover {
            background: #f0f7f0;
            color: #2e7d32;
        }

        .nav-item.active {
            background: #e8f5e9;
            color: #2e7d32;
            font-weight: 500;
        }

        .nav-item i {
            margin-right: 8px;
            width: 16px;
            text-align: center;
            font-size: 14px;
        }

        /* Main Content Styles */
        .main-content {
            margin-top: 60px;
            padding: 20px;
            width: 100%;
            box-sizing: border-box;
        }

        .header {
            background: transparent;
            padding: 0 0 15px 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        }

        .user-profile {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .user-profile span {
            padding: 6px 12px;
            background: white;
            border-radius: 4px;
            color: #2e7d32;
            font-size: 13px;
            font-weight: 500;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
        }

        .logout-btn {
            padding: 6px 12px;
            background: #ffebee;
            border-radius: 4px;
            color: #dc3545;
            text-decoration: none;
            font-size: 13px;
            font-weight: 500;
            transition: all 0.2s ease;
        }

        .logout-btn:hover {
            background: #dc3545;
            color: white;
        }

        /* Form Styles */
        .form-container {
            background: white;
            padding: 20px;
            margin: 20px;
            border-radius: 8px;
            border: 1px solid rgba(0, 0, 0, 0.05);
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.02);
            max-width: 800px;
            margin: 20px auto;
        }

        .form-title {
            color: #333;
            font-size: 16px;
            font-weight: 500;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #333;
            font-weight: 500;
            font-size: 14px;
        }

        .form-control {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            transition: all 0.2s ease;
        }

        .form-control:focus {
            outline: none;
            border-color: #2e7d32;
            box-shadow: 0 0 0 3px rgba(46, 125, 50, 0.1);
        }

        .form-control[readonly] {
            background-color: #f8f9fa;
            cursor: not-allowed;
        }

        .btn {
            padding: 8px 16px;
            border-radius: 4px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            border: none;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .btn-primary {
            background: #2e7d32;
            color: white;
        }

        .btn-secondary {
            background: #e8f5e9;
            color: #2e7d32;
            border: 1px solid #c8e6c9;
        }

        .btn-danger {
            background: #ffebee;
            color: #dc3545;
        }

        .btn-primary:hover {
            background: #1b5e20;
        }

        .btn-secondary:hover {
            background: #c8e6c9;
        }

        .btn-danger:hover {
            background: #dc3545;
            color: white;
        }

        .submit-btn {
            background: #2e7d32;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .submit-btn:hover {
            background: #1b5e20;
        }

        .success-message {
            background-color: #e8f5e9;
            color: #2e7d32;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
            font-size: 14px;
        }

        .error-message {
            background-color: #ffebee;
            color: #dc3545;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
            font-size: 14px;
        }

        .back-btn {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            color: #2e7d32;
            background: #e8f5e9;
            text-decoration: none;
            padding: 8px 16px;
            border-radius: 4px;
            font-size: 14px;
            font-weight: 500;
            margin-left: 20px;
            transition: all 0.2s ease;
        }

        .back-btn:hover {
            background: #c8e6c9;
        }

        .form-row {
            display: flex;
            gap: 20px;
            margin-bottom: 0;
        }

        .form-row .form-group {
            flex: 1;
        }

        .section-title {
            font-size: 16px;
            color: #2e7d32;
            font-weight: 500;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .section-title i {
            font-size: 14px;
        }
    </style>
</head>
<body>
    <!-- Topbar -->
    <div class="topbar">
        <div class="logo-section">

            <nav class="nav-menu">
            <a href="dashboard.php" class="nav-item">
                <i class="fas fa-home"></i>
                Dashboard
            </a>
            <?php if($current_user['role'] === 'super_admin' || $current_user['role'] === 'admin' || $current_user['role'] === 'staff'): ?>
            <a href="add_patient.php" class="nav-item">
                <i class="fas fa-user-plus"></i>
                Add Patient
            </a>
            <?php endif; ?>
            <a href="view_patients.php" class="nav-item">
                <i class="fas fa-users"></i>
                Patient Records
            </a>
            <?php if($current_user['role'] !== 'staff'): ?>
            <a href="manage_users.php" class="nav-item active">
                <i class="fas fa-users-cog"></i>
                User Management
            </a>
            <?php endif; ?>
            <a href="logout.php" class="nav-item">
                <i class="fas fa-sign-out-alt"></i>
                Logout
            </a>
        </nav>
        </div>
        <div class="user-profile">
            <span><?php echo htmlspecialchars($current_user_username); ?></span>
        </div>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <a href="manage_users.php" class="back-btn">
            <i class="fas fa-arrow-left"></i>
            Back to User Management
        </a>

        <!-- Edit User Form -->
        <div class="form-container">
            <div class="section-title">
                <i class="fas fa-user-edit"></i>
                Edit User: <?php echo htmlspecialchars($user['username']); ?>
            </div>

            <?php if(isset($success_message)): ?>
                <div class="success-message"><?php echo $success_message; ?></div>
            <?php endif; ?>

            <?php if(isset($error_message)): ?>
                <div class="error-message"><?php echo $error_message; ?></div>
            <?php endif; ?>

            <form method="POST" action="">
                <div class="form-row">
                <div class="form-group">
                    <label for="username">Username</label>
                        <input type="text" id="username" name="username" class="form-control" value="<?php echo htmlspecialchars($user['username']); ?>" required>
                </div>

                <div class="form-group">
                    <label for="fullname">Full Name</label>
                        <input type="text" id="fullname" name="fullname" class="form-control" value="<?php echo htmlspecialchars($user['fullname']); ?>" required>
                    </div>
                </div>

                <div class="form-row">
                <div class="form-group">
                    <label for="designation">Designation</label>
                        <input type="text" id="designation" name="designation" class="form-control" value="<?php echo htmlspecialchars($user['designation']); ?>" required>
                    </div>

                    <div class="form-group">
                        <label for="barangay">Facility</label>
                        <select id="barangay" name="barangay" class="form-control" required>
                            <option value="">Select Facility</option>
                            <option value="Baclaran Health Center" <?php echo $user['barangay'] === 'Baclaran Health Center' ? 'selected' : ''; ?>>Baclaran Health Center</option>
                            <option value="BF Homes Health Center" <?php echo $user['barangay'] === 'BF Homes Health Center' ? 'selected' : ''; ?>>BF Homes Health Center</option>
                            <option value="Don Bosco Health Center" <?php echo $user['barangay'] === 'Don Bosco Health Center' ? 'selected' : ''; ?>>Don Bosco Health Center</option>
                            <option value="Don Galo Health Center" <?php echo $user['barangay'] === 'Don Galo Health Center' ? 'selected' : ''; ?>>Don Galo Health Center</option>
                            <option value="La Huerta Health Center" <?php echo $user['barangay'] === 'La Huerta Health Center' ? 'selected' : ''; ?>>La Huerta Health Center</option>
                            <option value="Marcelo Green Health Center" <?php echo $user['barangay'] === 'Marcelo Green Health Center' ? 'selected' : ''; ?>>Marcelo Green Health Center</option>
                            <option value="Merville Health Center" <?php echo $user['barangay'] === 'Merville Health Center' ? 'selected' : ''; ?>>Merville Health Center</option>
                            <option value="Moonwalk Health Center" <?php echo $user['barangay'] === 'Moonwalk Health Center' ? 'selected' : ''; ?>>Moonwalk Health Center</option>
                            <option value="San Antonio Health Center" <?php echo $user['barangay'] === 'San Antonio Health Center' ? 'selected' : ''; ?>>San Antonio Health Center</option>
                            <option value="San Dionisio Health Center" <?php echo $user['barangay'] === 'San Dionisio Health Center' ? 'selected' : ''; ?>>San Dionisio Health Center</option>
                            <option value="San Isidro Health Center" <?php echo $user['barangay'] === 'San Isidro Health Center' ? 'selected' : ''; ?>>San Isidro Health Center</option>
                            <option value="San Martin De Porres Health Center" <?php echo $user['barangay'] === 'San Martin De Porres Health Center' ? 'selected' : ''; ?>>San Martin De Porres Health Center</option>
                            <option value="Santo Niño Health Center" <?php echo $user['barangay'] === 'Santo Niño Health Center' ? 'selected' : ''; ?>>Santo Niño Health Center</option>
                            <option value="Sun Valley Health Center" <?php echo $user['barangay'] === 'Sun Valley Health Center' ? 'selected' : ''; ?>>Sun Valley Health Center</option>
                            <option value="Tambo Health Center" <?php echo $user['barangay'] === 'Tambo Health Center' ? 'selected' : ''; ?>>Tambo Health Center</option>
                            <option value="Vitalez Health Center" <?php echo $user['barangay'] === 'Vitalez Health Center' ? 'selected' : ''; ?>>Vitalez Health Center</option>
                        </select>
                    </div>
                </div>

                <div class="form-row">
                <div class="form-group">
                    <label for="role">Role</label>
                        <select id="role" name="role" class="form-control" required>
                        <option value="super_admin" <?php echo $user['role'] === 'super_admin' ? 'selected' : ''; ?>>Super Admin</option>
                        <option value="admin" <?php echo $user['role'] === 'admin' ? 'selected' : ''; ?>>Admin</option>
                        <option value="staff" <?php echo $user['role'] === 'staff' ? 'selected' : ''; ?>>Staff</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="status">Status</label>
                        <select id="status" name="status" class="form-control" required>
                        <option value="active" <?php echo $user['status'] === 'active' ? 'selected' : ''; ?>>Active</option>
                        <option value="inactive" <?php echo $user['status'] === 'inactive' ? 'selected' : ''; ?>>Inactive</option>
                    </select>
                </div>
                </div>

                <div class="form-group">
                    <label for="password">New Password (leave blank to keep current)</label>
                    <input type="password" id="password" name="password" class="form-control" placeholder="Enter new password">
                </div>

                <div style="display: flex; gap: 10px; justify-content: flex-end; margin-top: 20px;">
                    <a href="manage_users.php" class="btn btn-secondary">
                        <i class="fas fa-times"></i> Cancel
                    </a>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> Update User
                    </button>
                </div>
            </form>
        </div>
    </div>
</body>
</html>