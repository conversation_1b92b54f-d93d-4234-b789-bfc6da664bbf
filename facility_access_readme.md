# Facility Access System

## Overview

The Facility Access System is designed to ensure that users automatically gain access to patient records registered under their assigned facility. When a user's facility assignment changes, they immediately gain access to all patient records at the new facility and lose access to records from their previous facility.

## Components

The system consists of the following components:

### 1. Facility Access Handler (`facility_access_handler.php`)

This component provides the core functionality for handling facility access changes:

- `handleFacilityChange($conn, $user_id, $old_facility, $new_facility)`: Logs facility changes and manages access rights updates.
- `userHasAccessToPatient($conn, $user_id, $patient_id)`: Checks if a specific user has access to a specific patient based on facility assignment.
- `countPatientsInFacility($conn, $facility)`: Counts the number of patients registered at a specific facility.

### 2. User Edit Handling (`edit_user.php`)

The user edit page has been enhanced to:

- Track facility changes during user profile edits
- Log facility changes as special activity events
- Automatically update access rights when a facility changes
- Provide feedback about the number of patient records the user now has access to

### 3. Patient Access Verification (`patient_access_check.php`)

This script can be included at the beginning of any page that displays patient details to:

- Ensure users only access patients from their assigned facility
- Log unauthorized access attempts
- Redirect users with appropriate error messages when they lack access

### 4. View Patients Page (`view_patients.php`)

The patient listing page now:

- Always fetches the most up-to-date facility information for the current user
- Ensures users only see patients from their currently assigned facility

## How It Works

1. **User Facility Assignment**: Each user is assigned to a specific health center/facility (barangay).

2. **Access Control**:
   - Regular users and admins can only access patient records from their assigned facility
   - Super admins can access all patient records across all facilities

3. **Facility Change Process**:
   - When an admin changes a user's facility assignment in the Edit User page
   - The system detects the change and logs it as a "Facility Change" event
   - The `handleFacilityChange()` function is called, logging a "Facility Access Change" event
   - The user immediately gains access to all patient records at the new facility
   - The user loses access to patient records from their previous facility
   - A success message indicates how many patient records the user now has access to

4. **Access Enforcement**:
   - The `view_patients.php` page always refreshes the user's facility data to ensure access control is based on current facility assignment
   - The `patient_access_check.php` script can be included in any patient detail page to prevent unauthorized access
   - Unauthorized access attempts are logged and users are redirected with an error message

## Implementation

To implement this system in a new patient detail page, include the patient access check at the beginning of the file:

```php
<?php
include 'patient_access_check.php';
// Rest of the patient detail page code
?>
```

## Security Considerations

- All user facility changes are logged with both the old and new facility
- Unauthorized access attempts are logged with user and patient details
- Prepared statements are used for all database queries to prevent SQL injection
- Access checks are performed server-side to prevent manipulation by client-side scripts 