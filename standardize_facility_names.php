<?php
session_start();
include 'config.php';

// Check if running from command line
$is_cli = (php_sapi_name() === 'cli');

// Only proceed if running from CLI or logged in as admin
if(!$is_cli && (!isset($_SESSION['user_id']) || ($_SESSION['role'] !== 'super_admin' && $_SESSION['role'] !== 'admin'))) {
    echo "Access denied. Only administrators can run this script.";
    exit;
}

echo "Starting comprehensive facility name standardization...\n";
$count = 0;

// Get all users and their facility names
$sql = "SELECT id, username, barangay FROM users ORDER BY barangay";
$result = $conn->query($sql);

if (!$result) {
    echo "Error executing query: " . $conn->error . "\n";
    exit;
}

// Standard facility format: append "Health Center" if missing
$standard_facilities = [
    "Baclaran", "BF Homes", "Don Bosco", "Don Galo", "La Huerta",
    "Marcelo Green", "Merville", "Moonwalk", "San Antonio", "San Dionisio", 
    "San Isidro", "San Martin De Porres", "Santo Niño", "Sun Valley", "Tambo", "Vitalez"
];

// Create mapping of standard facility names
$facility_map = [];
foreach ($standard_facilities as $facility) {
    $facility_map[strtolower($facility)] = $facility . " Health Center";
}

if ($result->num_rows > 0) {
    echo "Found " . $result->num_rows . " total users to process.\n";
    
    // Process each user's facility name
    while($row = $result->fetch_assoc()) {
        $old_value = $row['barangay'];
        $new_value = $old_value;
        $needs_update = false;
        
        // 1. Fix duplicated words
        $words = explode(' ', $old_value);
        $unique_words = [];
        foreach ($words as $word) {
            // Only add the word if it's not already the last word added (prevents duplicates)
            if (empty($unique_words) || strtolower(end($unique_words)) !== strtolower($word)) {
                $unique_words[] = $word;
            }
        }
        $deduplicated = implode(' ', $unique_words);
        
        if ($deduplicated !== $old_value) {
            $new_value = $deduplicated;
            $needs_update = true;
        }
        
        // 2. Standardize facility name format if missing "Health Center"
        if (!stripos($new_value, "Health Center")) {
            // Check if this is a known facility that should have "Health Center" appended
            foreach ($facility_map as $base => $standard) {
                if (stripos(strtolower($new_value), $base) !== false) {
                    $new_value = $standard;
                    $needs_update = true;
                    break;
                }
            }
        }
        
        // Update the database if changes were made
        if ($needs_update) {
            $update_sql = "UPDATE users SET barangay = ? WHERE id = ?";
            $stmt = $conn->prepare($update_sql);
            $stmt->bind_param("si", $new_value, $row['id']);
            
            if ($stmt->execute()) {
                $count++;
                echo "Fixed: User '" . $row['username'] . "' - Changed from '" . $old_value . "' to '" . $new_value . "'\n";
            } else {
                echo "Error updating user " . $row['username'] . ": " . $stmt->error . "\n";
            }
        }
    }
} else {
    echo "No users found in the database.\n";
}

echo "\nStandardization complete. Updated " . $count . " user records.\n";
$conn->close();
?> 