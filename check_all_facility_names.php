<?php
session_start();
include 'config.php';

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Check if running from command line
$is_cli = (php_sapi_name() === 'cli');

// Only proceed if running from CLI or logged in as admin
if(!$is_cli && (!isset($_SESSION['user_id']) || ($_SESSION['role'] !== 'super_admin' && $_SESSION['role'] !== 'admin'))) {
    echo "Access denied. Only administrators can run this script.";
    exit;
}

echo "Comprehensive Facility Name Audit\n";
echo "================================\n\n";

// Define standard facility list
$standard_facilities = [
    "Baclaran Health Center", 
    "BF Homes Health Center", 
    "Don Bosco Health Center", 
    "Don Galo Health Center", 
    "La Huerta Health Center",
    "Marcelo Green Health Center", 
    "Merville Health Center", 
    "Moonwalk Health Center", 
    "San Antonio Health Center", 
    "San Dionisio Health Center", 
    "San Isidro Health Center", 
    "San Martin De Porres Health Center", 
    "Santo Niño Health Center", 
    "Sun Valley Health Center", 
    "Tambo Health Center", 
    "Vitalez Health Center"
];

// Create standardized versions for reference (all lowercase for comparison)
$standard_names_lower = array_map('strtolower', $standard_facilities);

// Get all users and their facility names
$sql = "SELECT id, username, fullname, barangay FROM users ORDER BY barangay, username";
$result = $conn->query($sql);

if (!$result) {
    echo "Error executing query: " . $conn->error . "\n";
    exit;
}

if ($result->num_rows > 0) {
    echo "Found " . $result->num_rows . " total users.\n\n";
    
    // Store all facility names to check for consistency
    $facility_names = [];
    $users_by_facility = [];
    $non_standard_facilities = [];
    
    // Check each user
    echo "User Facility Details:\n";
    echo "---------------------\n";
    while($row = $result->fetch_assoc()) {
        $username = $row['username'];
        $fullname = $row['fullname'];
        $facility = $row['barangay'];
        
        echo "User: " . str_pad($username, 15) . "Name: " . str_pad($fullname, 30) . "Facility: " . $facility . "\n";
        
        $facility_names[$facility] = isset($facility_names[$facility]) ? $facility_names[$facility] + 1 : 1;
        $users_by_facility[$facility][] = $username;
        
        // Check if this is a standard facility name
        if (!in_array(strtolower($facility), $standard_names_lower)) {
            $non_standard_facilities[$facility] = true;
        }
        
        // Check for duplicated words
        $words = explode(' ', strtolower($facility));
        $dup_words = array_diff_assoc($words, array_unique($words));
        
        if (!empty($dup_words)) {
            echo "  - WARNING: Duplicated words found: " . implode(', ', array_unique($dup_words)) . "\n";
        }
    }
    
    echo "\nFacility Distribution Summary:\n";
    echo "----------------------------\n";
    foreach ($facility_names as $name => $count) {
        echo $name . " (" . $count . " users): " . implode(", ", $users_by_facility[$name]) . "\n";
    }
    
    if (!empty($non_standard_facilities)) {
        echo "\nNon-Standard Facility Names:\n";
        echo "--------------------------\n";
        foreach (array_keys($non_standard_facilities) as $name) {
            echo "- " . $name . " (Users: " . implode(", ", $users_by_facility[$name]) . ")\n";
            
            // Suggest standardization
            $closest = null;
            $highest_similarity = 0;
            foreach ($standard_facilities as $standard) {
                $similarity = similar_text(strtolower($name), strtolower($standard), $percent);
                if ($percent > $highest_similarity) {
                    $highest_similarity = $percent;
                    $closest = $standard;
                }
            }
            
            if ($closest && $highest_similarity > 50) {
                echo "  Suggestion: Change to \"" . $closest . "\" (similarity: " . number_format($highest_similarity, 1) . "%)\n";
            }
        }
    } else {
        echo "\nAll facility names follow the standard format.\n";
    }
} else {
    echo "No users found in the database.\n";
}

$conn->close();
echo "\nAudit complete.\n";
?> 