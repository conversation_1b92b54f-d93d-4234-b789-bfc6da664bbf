<?php
session_start();
include 'config.php';

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Check if running from command line
$is_cli = (php_sapi_name() === 'cli');

// Only proceed if running from CLI or logged in as admin
if(!$is_cli && (!isset($_SESSION['user_id']) || ($_SESSION['role'] !== 'super_admin' && $_SESSION['role'] !== 'admin'))) {
    echo "Access denied. Only administrators can run this script.";
    exit;
}

echo "Checking all user facility names...\n\n";

// Get all users and their facility names
$sql = "SELECT id, username, barangay FROM users ORDER BY barangay";
$result = $conn->query($sql);

if (!$result) {
    echo "Error executing query: " . $conn->error . "\n";
    exit;
}

if ($result->num_rows > 0) {
    echo "Found " . $result->num_rows . " total users.\n";
    
    // Store all facility names to check for consistency
    $facility_names = [];
    
    // Check each user
    while($row = $result->fetch_assoc()) {
        echo "User: " . $row['username'] . ", Facility: " . $row['barangay'] . "\n";
        $facility = $row['barangay'];
        $facility_names[$facility] = isset($facility_names[$facility]) ? $facility_names[$facility] + 1 : 1;
        
        // Check for duplicated words
        $words = explode(' ', strtolower($facility));
        $dup_words = array_diff_assoc($words, array_unique($words));
        
        if (!empty($dup_words)) {
            echo "- WARNING: User '" . $row['username'] . "' has duplicated words in facility name: '" . $facility . "'\n";
            echo "  Duplicated words: " . implode(', ', array_unique($dup_words)) . "\n";
        }
    }
    
    echo "\nFacility name distribution:\n";
    foreach ($facility_names as $name => $count) {
        echo "- '$name': $count users\n";
    }
} else {
    echo "No users found in the database.\n";
}

$conn->close();
echo "\nCheck complete.\n";
?> 