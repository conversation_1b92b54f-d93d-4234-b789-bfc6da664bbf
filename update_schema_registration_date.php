<?php
session_start();
include 'config.php';

// Add registration_date column to patients table if it doesn't exist
$check_column = "SHOW COLUMNS FROM patients LIKE 'registration_date'";
$result = $conn->query($check_column);

if ($result->num_rows == 0) {
    // Column doesn't exist, add it
    $add_column = "ALTER TABLE patients ADD COLUMN registration_date DATE NULL AFTER purpose";
    
    if ($conn->query($add_column) === TRUE) {
        echo "Column 'registration_date' added successfully.<br>";
    } else {
        echo "Error adding column: " . $conn->error . "<br>";
    }
} else {
    echo "Column 'registration_date' already exists.<br>";
}

// Add barangay column to patients table if it doesn't exist
$check_column = "SHOW COLUMNS FROM patients LIKE 'barangay'";
$result = $conn->query($check_column);

if ($result->num_rows == 0) {
    // Column doesn't exist, add it
    $add_column = "ALTER TABLE patients ADD COLUMN barangay VARCHAR(100) NULL AFTER purpose";
    
    if ($conn->query($add_column) === TRUE) {
        echo "Column 'barangay' added successfully.<br>";
    } else {
        echo "Error adding column: " . $conn->error . "<br>";
    }
} else {
    echo "Column 'barangay' already exists.<br>";
}

echo "Schema update completed.";
?> 