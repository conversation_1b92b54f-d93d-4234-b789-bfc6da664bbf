<?php
session_start();
include 'config.php';

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "Patient Facility Name Fixing Tool\n";
echo "================================\n\n";

// Get the mapping of standard facility names
$standard_names = [
    'Baclaran' => 'Baclaran Health Center',
    'BF Homes' => 'BF Homes Health Center',
    'Don Bosco' => 'Don Bosco Health Center',
    'Don Galo' => 'Don Galo Health Center',
    'La Huerta' => 'La Huerta Health Center',
    'Marcelo Green' => 'Marcelo Green Health Center',
    'Merville' => 'Merville Health Center',
    'Moonwalk' => 'Moonwalk Health Center',
    'San Antonio' => 'San Antonio Health Center',
    'San Dionisio' => 'San Dionisio Health Center',
    'San Isidro' => 'San Isidro Health Center',
    'San Martin De Porres' => 'San Martin De Porres Health Center',
    'Santo Niño' => 'Santo Niño Health Center',
    'Sun Valley' => 'Sun Valley Health Center',
    'Tambo' => 'Tambo Health Center',
    'Vitalez' => 'Vitalez Health Center',
];

// Fix special cases
$special_cases = [
    'B. F. Homes' => 'BF Homes Health Center',
    'Sto. Niño' => 'Santo Niño Health Center',
];

// Get all distinct facility names from patients table
$sql = "SELECT DISTINCT barangay FROM patients";
$result = $conn->query($sql);

echo "Found the following facility names in patients table:\n";
$patient_facilities = [];
if ($result->num_rows > 0) {
    while ($row = $result->fetch_assoc()) {
        $patient_facilities[] = $row['barangay'];
        echo "- " . $row['barangay'] . "\n";
    }
}

echo "\nUpdating patient facility names to match standard format...\n";

// Update each facility name
$updated_count = 0;

foreach ($patient_facilities as $facility) {
    $new_facility = null;
    
    // Check if it's a special case
    if (array_key_exists($facility, $special_cases)) {
        $new_facility = $special_cases[$facility];
    } 
    // Check if it already has "Health Center" suffix
    elseif (strpos($facility, "Health Center") !== false) {
        echo "'{$facility}' already has 'Health Center' suffix - skipping\n";
        continue; // Already has "Health Center" suffix
    } 
    // Check if it matches a standard name without the suffix
    elseif (array_key_exists($facility, $standard_names)) {
        $new_facility = $standard_names[$facility];
    }
    
    if ($new_facility) {
        // Count affected rows before update
        $count_sql = "SELECT COUNT(*) as count FROM patients WHERE barangay = ?";
        $stmt = $conn->prepare($count_sql);
        $stmt->bind_param("s", $facility);
        $stmt->execute();
        $count_result = $stmt->get_result();
        $count_row = $count_result->fetch_assoc();
        $affected_rows = $count_row['count'];
        
        // Update the facility name
        $update_sql = "UPDATE patients SET barangay = ? WHERE barangay = ?";
        $stmt = $conn->prepare($update_sql);
        $stmt->bind_param("ss", $new_facility, $facility);
        if ($stmt->execute()) {
            $updated_count += $affected_rows;
            echo "Updated '{$facility}' to '{$new_facility}' ({$affected_rows} records)\n";
        } else {
            echo "Error updating '{$facility}': " . $stmt->error . "\n";
        }
    } else {
        echo "WARNING: No standard name found for '{$facility}'\n";
    }
}

echo "\nUpdate completed. {$updated_count} patient records were updated.\n";

// Verify the updates
echo "\nVerifying facility names after update:\n";
$sql = "SELECT DISTINCT barangay, COUNT(*) as count FROM patients GROUP BY barangay ORDER BY count DESC";
$result = $conn->query($sql);

if ($result->num_rows > 0) {
    echo "\nCurrent facility distribution in patients table:\n";
    while ($row = $result->fetch_assoc()) {
        echo "- " . $row['barangay'] . ": " . $row['count'] . " patients\n";
    }
} else {
    echo "No patient records found.\n";
}

echo "\nDone.\n";
?> 