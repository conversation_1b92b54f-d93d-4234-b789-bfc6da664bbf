<?php
session_start();
include 'config.php';

// Check if running from command line
$is_cli = (php_sapi_name() === 'cli');

// Only proceed if running from CLI or logged in as admin
if(!$is_cli && (!isset($_SESSION['user_id']) || ($_SESSION['role'] !== 'super_admin' && $_SESSION['role'] !== 'admin'))) {
    echo "Access denied. Only administrators can run this script.";
    exit;
}

// Patterns to search for and their replacements
$patterns = [
    "HEALTH CENTER HEALTH CENTER" => "HEALTH CENTER",
    "Health Center Health Center" => "Health Center",
    "health center health center" => "health center"
];

echo "Starting facility name cleanup...\n";
$count = 0;

foreach ($patterns as $search => $replace) {
    // Find users with duplicated "HEALTH CENTER" text
    $sql = "SELECT id, username, barangay FROM users WHERE barangay LIKE '%$search%'";
    $result = $conn->query($sql);
    
    if ($result->num_rows > 0) {
        echo "Found " . $result->num_rows . " users with '$search' in their facility name.\n";
        
        while($row = $result->fetch_assoc()) {
            $old_value = $row['barangay'];
            $new_value = str_replace($search, $replace, $old_value);
            
            // Update the user's facility name
            $update_sql = "UPDATE users SET barangay = ? WHERE id = ?";
            $stmt = $conn->prepare($update_sql);
            $stmt->bind_param("si", $new_value, $row['id']);
            
            if ($stmt->execute()) {
                $count++;
                echo "Fixed: User '" . $row['username'] . "' - Changed from '" . $old_value . "' to '" . $new_value . "'\n";
            } else {
                echo "Error updating user " . $row['username'] . ": " . $stmt->error . "\n";
            }
        }
    }
}

echo "Cleanup complete. Updated " . $count . " user records.\n";
$conn->close();
?> 