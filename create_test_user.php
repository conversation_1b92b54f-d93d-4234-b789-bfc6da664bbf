<?php
// Enable error reporting
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Include database connection
include 'config.php';

echo "<h1>Create Test User in Pending Status</h1>";

// Generate a unique username
$username = "testuser_" . time();
$password = password_hash("password123", PASSWORD_DEFAULT);
$fullname = "Test User";
$designation = "Test Designation";
$barangay = "Test Barangay";
$role = "staff";
$status = "pending";

// Insert the test user
$sql = "INSERT INTO users (username, password, fullname, designation, barangay, role, status, created_at) 
        VALUES (?, ?, ?, ?, ?, ?, ?, NOW())";
$stmt = $conn->prepare($sql);
$stmt->bind_param("sssssss", $username, $password, $fullname, $designation, $barangay, $role, $status);

if ($stmt->execute()) {
    $userId = $stmt->insert_id;
    echo "<p>Test user created successfully with ID: $userId</p>";
    echo "<p>Username: $username</p>";
    echo "<p>Status: $status</p>";
    
    // Verify the user was inserted correctly
    $verify_sql = "SELECT id, username, status FROM users WHERE id = ?";
    $verify_stmt = $conn->prepare($verify_sql);
    $verify_stmt->bind_param("i", $userId);
    $verify_stmt->execute();
    $result = $verify_stmt->get_result();
    
    if ($row = $result->fetch_assoc()) {
        echo "<p>Verification successful:</p>";
        echo "<p>ID: " . $row['id'] . "</p>";
        echo "<p>Username: " . $row['username'] . "</p>";
        echo "<p>Status: " . $row['status'] . "</p>";
    } else {
        echo "<p>Verification failed. User not found after insertion.</p>";
    }
    
    $verify_stmt->close();
} else {
    echo "<p>Error creating test user: " . $stmt->error . "</p>";
}

$stmt->close();

echo "<p><a href='manage_users.php'>Go to Manage Users page</a> to see the pending user.</p>";
?> 