<?php
session_start();

// Set rejection session variables
$_SESSION['error'] = "Your account has been rejected.";
$_SESSION['rejected'] = true;
$_SESSION['rejected_message'] = "Your registration request has been reviewed and denied by an administrator. If you believe this is an error, please contact the system administrator.";
$_SESSION['rejection_reason'] = "Invalid credentials provided during registration.";
$_SESSION['rejection_date'] = date('Y-m-d');

// Redirect to login page
header("Location: login.php");
exit();
?>

<?php
session_start();

// Set rejection session variables
$_SESSION['error'] = "Your account has been rejected.";
$_SESSION['rejected'] = true;
$_SESSION['rejected_message'] = "Your registration request has been reviewed and denied by an administrator. If you believe this is an error, please contact the system administrator.";
$_SESSION['rejection_reason'] = "Your account information did not meet our requirements.";
$_SESSION['rejection_date'] = date('Y-m-d');

// Debug information to help troubleshooting
echo "Session variables set:<br>";
echo "error: " . $_SESSION['error'] . "<br>";
echo "rejected: " . ($_SESSION['rejected'] ? 'true' : 'false') . "<br>";
echo "rejected_message: " . $_SESSION['rejected_message'] . "<br>";
echo "rejection_reason: " . $_SESSION['rejection_reason'] . "<br>";
echo "rejection_date: " . $_SESSION['rejection_date'] . "<br>";

echo "<p>Click <a href='login.php'>here</a> to continue to login.</p>";
?> 