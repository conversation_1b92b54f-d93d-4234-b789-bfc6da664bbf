<?php
session_start();
include 'config.php';

// Check if user is logged in and is admin or super_admin
if (!isset($_SESSION['user_id']) || ($_SESSION['role'] !== 'admin' && $_SESSION['role'] !== 'super_admin')) {
    header("Location: login.php");
    exit();
}

// Check if user ID is provided
if (!isset($_GET['id'])) {
    $_SESSION['error'] = "No user specified.";
    header("Location: manage_users.php");
    exit();
}

$user_id = $_GET['id'];

// Get user details
$user_sql = "SELECT id, username, fullname, role, status FROM users WHERE id = ?";
$user_stmt = $conn->prepare($user_sql);
$user_stmt->bind_param("i", $user_id);
$user_stmt->execute();
$user_result = $user_stmt->get_result();

if ($user_result->num_rows === 0) {
    $_SESSION['error'] = "User not found.";
    header("Location: manage_users.php");
    exit();
}

$user = $user_result->fetch_assoc();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>User Activity - <?php echo htmlspecialchars($user['username']); ?></title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Poppins', sans-serif;
            background-color: #f5f5f5;
            margin: 0;
            padding: 0;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            background-color: #388e3c;
            color: white;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }
        
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .user-info {
            background-color: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }
        
        .user-info h2 {
            margin-top: 0;
            color: #333;
            border-bottom: 1px solid #eee;
            padding-bottom: 10px;
        }
        
        .user-info-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 20px;
        }
        
        .info-item {
            margin-bottom: 15px;
        }
        
        .info-label {
            font-weight: 600;
            color: #666;
            margin-bottom: 5px;
        }
        
        .info-value {
            color: #333;
        }
        
        .back-button {
            background-color: transparent;
            color: white;
            border: 1px solid white;
            padding: 10px 20px;
            border-radius: 4px;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            transition: all 0.2s ease;
        }
        
        .back-button:hover {
            background-color: rgba(255, 255, 255, 0.1);
        }

        .activity-table {
            width: 100%;
            border-collapse: collapse;
            background-color: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }

        .activity-table th, .activity-table td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #eee;
        }

        .activity-table th {
            background-color: #f8f8f8;
            font-weight: 600;
            color: #333;
        }

        .activity-table tr:last-child td {
            border-bottom: none;
        }
        
        .status-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 30px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .status-active {
            background-color: #e8f5e9;
            color: #2e7d32;
        }
        
        .status-pending {
            background-color: #fff3e0;
            color: #f57c00;
        }
        
        .status-inactive {
            background-color: #f5f5f5;
            color: #757575;
        }
        
        .no-activity {
            text-align: center;
            padding: 30px;
            color: #666;
            font-style: italic;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="container">
            <div class="header-content">
                <h1>User Activity Log</h1>
                <a href="manage_users.php" class="back-button">
                    <i class="fas fa-arrow-left"></i> Back to Users
                </a>
            </div>
        </div>
    </div>

    <div class="container">
        <div class="user-info">
            <h2>User Information</h2>
            <div class="user-info-grid">
                <div class="info-item">
                    <div class="info-label">Username</div>
                    <div class="info-value"><?php echo htmlspecialchars($user['username']); ?></div>
                </div>
                <div class="info-item">
                    <div class="info-label">Full Name</div>
                    <div class="info-value"><?php echo htmlspecialchars($user['fullname']); ?></div>
                </div>
                <div class="info-item">
                    <div class="info-label">Role</div>
                    <div class="info-value"><?php echo ucfirst(htmlspecialchars($user['role'])); ?></div>
                </div>
                <div class="info-item">
                    <div class="info-label">Status</div>
                    <div class="info-value">
                        <span class="status-badge status-<?php echo strtolower($user['status']); ?>">
                            <?php echo ucfirst(htmlspecialchars($user['status'])); ?>
                        </span>
                    </div>
                </div>
            </div>
        </div>

        <h2>Activity History</h2>
        
        <?php
        // Check if user_activity table exists and get its structure
        $tableExists = $conn->query("SHOW TABLES LIKE 'user_activity'")->num_rows > 0;
        
        if ($tableExists) {
            // Get column names
            $columns = [];
            $columnsResult = $conn->query("SHOW COLUMNS FROM user_activity");
            while ($column = $columnsResult->fetch_assoc()) {
                $columns[] = $column['Field'];
            }
            
            // Build query based on table structure
            $activity_sql = "";
            if (in_array('action', $columns) && in_array('details', $columns)) {
                // Old structure
                $activity_sql = "SELECT * FROM user_activity WHERE user_id = ? ORDER BY created_at DESC LIMIT 100";
            } elseif (in_array('activity', $columns) && in_array('timestamp', $columns)) {
                // New structure
                $activity_sql = "SELECT * FROM user_activity WHERE user_id = ? ORDER BY timestamp DESC LIMIT 100";
            }
            
            if (!empty($activity_sql)) {
                $activity_stmt = $conn->prepare($activity_sql);
                $activity_stmt->bind_param("i", $user_id);
                $activity_stmt->execute();
                $activities = $activity_stmt->get_result();
                
                if ($activities->num_rows > 0) {
                    echo '<table class="activity-table">';
                    echo '<thead><tr>';
                    
                    // Dynamic headers based on columns
                    if (in_array('action', $columns)) echo '<th>Action</th>';
                    if (in_array('activity', $columns)) echo '<th>Activity</th>';
                    if (in_array('details', $columns)) echo '<th>Details</th>';
                    if (in_array('timestamp', $columns)) echo '<th>Time</th>';
                    else if (in_array('created_at', $columns)) echo '<th>Time</th>';
                    
                    echo '</tr></thead><tbody>';
                    
                    while ($row = $activities->fetch_assoc()) {
                        echo '<tr>';
                        if (in_array('action', $columns)) echo '<td>' . htmlspecialchars($row['action']) . '</td>';
                        if (in_array('activity', $columns)) echo '<td>' . htmlspecialchars($row['activity']) . '</td>';
                        if (in_array('details', $columns)) echo '<td>' . htmlspecialchars($row['details']) . '</td>';
                        
                        // Time column
                        if (in_array('timestamp', $columns)) {
                            echo '<td>' . date('M d, Y g:i A', strtotime($row['timestamp'])) . '</td>';
                        } else if (in_array('created_at', $columns)) {
                            echo '<td>' . date('M d, Y g:i A', strtotime($row['created_at'])) . '</td>';
                        }
                        
                        echo '</tr>';
                    }
                    
                    echo '</tbody></table>';
                } else {
                    echo '<div class="no-activity">No activity records found for this user.</div>';
                }
            } else {
                echo '<div class="no-activity">Unable to determine user activity table structure.</div>';
            }
        } else {
            echo '<div class="no-activity">User activity tracking is not set up.</div>';
        }
        ?>
    </div>

    <!-- JavaScript for sidebar functionality -->
    <script src="sidebar.js"></script>
</body>
</html> 