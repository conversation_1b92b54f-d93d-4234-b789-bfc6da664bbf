<?php
session_start();
include 'config.php';

// Check if user is logged in
if(!isset($_SESSION['user_id'])) {
    header("Location: login.php");
    exit();
}

// Get prescription ID from URL
$prescription_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if($prescription_id <= 0) {
    die("Invalid prescription ID");
}

// Get prescription details
$sql = "SELECT p.*, patient.fullname as patient_name, patient.age, patient.sex, 
        patient.address, patient.barangay, u.username as prescribed_by 
        FROM prescriptions p 
        JOIN patients patient ON p.patient_id = patient.id 
        LEFT JOIN users u ON p.created_by = u.id 
        WHERE p.id = ?";
$stmt = $conn->prepare($sql);
$stmt->bind_param("i", $prescription_id);
$stmt->execute();
$result = $stmt->get_result();

if($result->num_rows == 0) {
    die("Prescription not found");
}

$prescription = $result->fetch_assoc();

// Format date
$prescription_date = date('F j, Y', strtotime($prescription['prescription_date']));

// Get current date and time
$current_date = date('F j, Y');
$current_time = date('h:i A');
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Prescription #<?php echo $prescription_id; ?></title>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Poppins', sans-serif;
        }
        
        body {
            background: #fff;
            font-size: 14px;
            line-height: 1.6;
            color: #333;
            padding: 20px;
        }
        
        .print-container {
            max-width: 800px;
            margin: 0 auto;
            background: #fff;
            padding: 20px;
            border: 1px solid #ddd;
        }
        
        .header {
            text-align: center;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #2e7d32;
        }
        
        .header h1 {
            font-size: 24px;
            color: #2e7d32;
            margin-bottom: 5px;
        }
        
        .header h2 {
            font-size: 18px;
            color: #444;
            font-weight: 500;
        }
        
        .header p {
            font-size: 13px;
            color: #666;
        }
        
        .rx-symbol {
            font-size: 28px;
            font-weight: bold;
            color: #2e7d32;
            margin-right: 10px;
        }
        
        .patient-info {
            margin-bottom: 20px;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
        }
        
        .info-group {
            margin-bottom: 10px;
        }
        
        .info-group label {
            font-weight: 500;
            color: #555;
            display: block;
            margin-bottom: 2px;
            font-size: 13px;
        }
        
        .info-group span {
            display: block;
            padding: 5px 0;
            border-bottom: 1px dotted #ddd;
        }
        
        .prescription-content {
            margin-top: 20px;
            border: 1px solid #e0e0e0;
            padding: 15px;
            background-color: #f9f9f9;
            border-radius: 4px;
        }
        
        .prescription-section {
            margin-bottom: 20px;
        }
        
        .prescription-section h3 {
            font-size: 16px;
            color: #2e7d32;
            margin-bottom: 8px;
            padding-bottom: 5px;
            border-bottom: 1px solid #e0e0e0;
        }
        
        .prescription-section p {
            white-space: pre-line;
            padding: 5px 10px;
        }
        
        .footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #e0e0e0;
        }
        
        .signature-section {
            display: flex;
            justify-content: flex-end;
            align-items: flex-end;
            flex-direction: column;
            margin-top: 50px;
            padding-right: 50px;
        }
        
        .signature-line {
            width: 250px;
            border-bottom: 1px solid #555;
            margin-bottom: 5px;
        }
        
        .doctor-name {
            font-weight: 500;
            text-align: center;
            width: 250px;
        }
        
        .doctor-title {
            font-size: 12px;
            color: #555;
            text-align: center;
            width: 250px;
        }
        
        .print-info {
            margin-top: 20px;
            font-size: 12px;
            color: #777;
            text-align: center;
        }
        
        .print-button {
            display: block;
            margin: 20px auto;
            padding: 10px 20px;
            background: #2e7d32;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        
        .print-button:hover {
            background: #1b5e20;
        }
        
        @media print {
            .print-button, .print-info {
                display: none;
            }
            
            body {
                padding: 0;
                margin: 0;
            }
            
            .print-container {
                max-width: 100%;
                padding: 15px;
                border: none;
            }
        }
    </style>
</head>
<body>
    <div class="print-container">
        <div class="header">
            <h1>Health Center</h1>
            <h2>Medical Prescription</h2>
            <p>Prescription #<?php echo $prescription_id; ?> | <?php echo $prescription_date; ?></p>
        </div>
        
        <div class="patient-info">
            <div class="info-group">
                <label>Patient Name:</label>
                <span><?php echo htmlspecialchars($prescription['patient_name']); ?></span>
            </div>
            
            <div class="info-group">
                <label>Age/Sex:</label>
                <span><?php echo htmlspecialchars($prescription['age']); ?> years / <?php echo htmlspecialchars($prescription['sex']); ?></span>
            </div>
            
            <div class="info-group">
                <label>Address:</label>
                <span><?php echo htmlspecialchars($prescription['address']); ?></span>
            </div>
            
            <div class="info-group">
                <label>Facility/Barangay:</label>
                <span><?php echo htmlspecialchars($prescription['barangay']); ?></span>
            </div>
        </div>
        
        <div class="prescription-content">
            <div style="display: flex; align-items: center; margin-bottom: 15px;">
                <div class="rx-symbol">Rx</div>
                <div style="font-size: 18px; font-weight: 500;">Prescription</div>
            </div>
            
            <div class="prescription-section">
                <h3>Medications</h3>
                <p><?php echo nl2br(htmlspecialchars($prescription['medications'])); ?></p>
            </div>
            
            <div class="prescription-section">
                <h3>Dosage</h3>
                <p><?php echo nl2br(htmlspecialchars($prescription['dosage'])); ?></p>
            </div>
            
            <?php if(!empty($prescription['instructions'])): ?>
            <div class="prescription-section">
                <h3>Special Instructions</h3>
                <p><?php echo nl2br(htmlspecialchars($prescription['instructions'])); ?></p>
            </div>
            <?php endif; ?>
        </div>
        
        <div class="signature-section">
            <div class="signature-line"></div>
            <div class="doctor-name"><?php echo htmlspecialchars($prescription['prescribed_by']); ?></div>
            <div class="doctor-title">Healthcare Provider</div>
        </div>
        
        <div class="footer">
            <p style="font-size: 12px; color: #666;">This prescription was generated on <?php echo $current_date; ?> at <?php echo $current_time; ?>.</p>
        </div>
        
        <div class="print-info">This document is intended for medical purposes only.</div>
        
        <button class="print-button" onclick="window.print()">Print Prescription</button>
    </div>
    
    <script>
        // Auto-print when page loads
        window.onload = function() {
            // Small delay to ensure page is fully loaded
            setTimeout(function() {
                window.print();
            }, 500);
        };
    </script>
</body>
</html> 