document.addEventListener('DOMContentLoaded', function() {
    // Add a class to body when page is fully loaded for additional animations
    document.body.classList.add('page-loaded');
    
    // Add hover effect to nav items
    const navItems = document.querySelectorAll('.nav-item');
    navItems.forEach(item => {
        item.addEventListener('mouseenter', function() {
            const icon = this.querySelector('i');
            if (icon) {
                icon.classList.add('animated');
                setTimeout(() => {
                    icon.classList.remove('animated');
                }, 300);
            }
        });
    });
    
    // Add ripple effect to buttons
    const buttons = document.querySelectorAll('.nav-item, .user-profile span, .logout-btn');
    buttons.forEach(button => {
        button.addEventListener('click', function(e) {
            const ripple = document.createElement('span');
            ripple.classList.add('ripple-effect');
            
            const rect = this.getBoundingClientRect();
            const size = Math.max(rect.width, rect.height);
            
            ripple.style.width = ripple.style.height = `${size}px`;
            ripple.style.left = `${e.clientX - rect.left - size/2}px`;
            ripple.style.top = `${e.clientY - rect.top - size/2}px`;
            
            this.appendChild(ripple);
            
            setTimeout(() => {
                ripple.remove();
            }, 600);
        });
    });
});
