<?php
session_start();
include 'config.php';

// Check if already logged in
if(isset($_SESSION['user_id'])) {
    header("Location: dashboard.php");
    exit();
}

// Check for error messages
$error = "";
if(isset($_SESSION['error'])) {
    $error = $_SESSION['error'];
    // Do not unset session variables yet, they will be used to display messages
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - Barangay Health Center</title>
    <link intttps://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="style.css">
    <style>
        /* All existing styles remain unchanged */
        body {
            background: linear-gradient(135deg, #e8f5e9 0%, #c8e6c9 100%);
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            font-family: 'Poppins', sans-serif;
            padding: 20px;
        }
        
        .login-container {
            display: flex;
            width: 900px;
            max-width: 100%;
            height: 500px;
            background: white;
            border-radius: 16px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
        }
        
        .welcome-panel {
            width: 40%;
            background: linear-gradient(135deg, #2e7d32 0%, #388e3c 100%);
            color: white;
            padding: 40px 30px;
            display: flex;
            flex-direction: column;
            position: relative;
            overflow: hidden;
        }
        
        .welcome-panel::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjMwMCIgdmlld0JveD0iMCAwIDMwMCAzMDAiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGRlZnM+PGxpbmVhckdyYWRpZW50IGlkPSJhIiB4MT0iMCUiIHkxPSIwJSIgeDI9IjEwMCUiIHkyPSIxMDAlIj48c3RvcCBzdG9wLWNvbG9yPSIjZmZmIiBzdG9wLW9wYWNpdHk9Ii4yIiBvZmZzZXQ9IjAlIi8+PHN0b3Agc3RvcC1jb2xvcj0iI2ZmZiIgc3RvcC1vcGFjaXR5PSIwIiBvZmZzZXQ9IjEwMCUiLz48L2xpbmVhckdyYWRpZW50PjwvZGVmcz48cGF0aCBkPSJNMzAwIDI3MHYzMEgwVjBIMzAwdjI3MHoiIGZpbGw9InVybCgjYSkiIGZpbGwtcnVsZT0iZXZlbm9kZCIvPjwvc3ZnPg==');
            opacity: 0.7;
            z-index: 0;
        }
        
        .welcome-image {
            align-self: center;
            margin-top: 50px;
            margin-bottom: 50px;
            z-index: 1;
            display: flex;
            justify-content: center;
            width: 100%;
        }
        
        .welcome-image img {
            width: auto;
            max-width: 180px;
            height: auto;
            filter: drop-shadow(0 5px 10px rgba(0, 0, 0, 0.2));
        }
        
        .system-intro {
            font-size: 13px;
            opacity: 0.9;
            margin-top: auto;
            text-align: center;
            z-index: 1;
            line-height: 1.4;
            font-weight: 500;
        }
        
        .login-panel {
            width: 60%;
            padding: 40px;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }
        
        .login-header {
            text-align: center;
            margin-bottom: 20px;
        }
        
        .login-title {
            background: linear-gradient(135deg, #2e7d32 0%, #388e3c 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            font-size: 24px;
            font-weight: 600;
            letter-spacing: 1px;
            margin-bottom: 5px;
        }
        
        .form-group {
            margin-bottom: 24px;
        }
        
        .form-group label {
            background: linear-gradient(135deg, #2e7d32 0%, #388e3c 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            display: block;
            margin-bottom: 8px;
            font-size: 14px;
            font-weight: 500;
        }
        
        .input-group {
            position: relative;
        }
        
        .input-group input {
            width: 100%;
            padding: 12px 40px 12px 40px;
            border: none;
            border-bottom: 2px solid #e0e0e0;
            font-size: 14px;
            transition: all 0.3s ease;
            background: transparent;
            padding-right: 40px;
        }
        
        .input-group input:focus {
            outline: none;
            border-bottom: 2px solid transparent;
            border-image: linear-gradient(135deg, #2e7d32 0%, #388e3c 100%);
            border-image-slice: 1;
        }
        
        .input-group i.fas.fa-lock,
        .input-group i.fas.fa-user {
            position: absolute;
            left: 12px;
            top: 50%;
            transform: translateY(-50%);
            background: linear-gradient(135deg, #2e7d32 0%, #388e3c 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }
        
        .input-group .toggle-password {
            position: absolute;
            right: 12px;
            top: 50%;
            transform: translateY(-50%);
            color: #388e3c;
            cursor: pointer;
            z-index: 2;
        }
        
        .login-btn {
            background: linear-gradient(135deg, #2e7d32 0%, #388e3c 100%);
            color: white;
            border: none;
            border-radius: 30px;
            padding: 12px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            width: 100%;
            margin-top: 10px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 10px rgba(46, 125, 50, 0.3);
        }
        
        .login-btn:hover {
            background: linear-gradient(135deg, #388e3c 0%, #2e7d32 100%);
            box-shadow: 0 6px 15px rgba(46, 125, 50, 0.4);
            transform: translateY(-2px);
        }
        
        .login-footer {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 8px;
            margin-top: 15px;
            font-size: 13px;
        }
        
        .login-footer span {
            color: #666;
        }
        
        .register-link {
            color: #388e3c;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .register-link:hover {
            background: linear-gradient(135deg, #2e7d32 0%, #388e3c 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            transform: translateY(-1px);
        }
        
        .error {
            background: #ffebee;
            color: #e53935;
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 15px;
            font-size: 14px;
            text-align: center;
        }
        
        .alert {
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 15px;
            font-size: 14px;
        }
        
        .alert-danger {
            background: #ffebee;
            color: #e53935;
            text-align: center;
        }
        
        .success-message {
            background: #e8f5e9;
            color: #2e7d32;
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 15px;
            font-size: 14px;
            text-align: center;
        }
        
        .alert-pending {
            margin-bottom: 15px;
            border-radius: 4px;
        }
        
        .pending-notice {
            margin-top: 5px;
            background: #fff3e0;
            color: #f57c00;
            padding: 8px;
            border-radius: 4px;
            font-size: 13px;
            text-align: center;
        }
        
        .pending-notice i {
            margin-right: 5px;
        }
        
        .rejected-message {
            display: flex;
            align-items: flex-start;
            gap: 10px;
            padding: 5px;
        }
        
        .rejected-message i {
            font-size: 24px;
            color: #d32f2f;
        }
        
        .rejected-message strong {
            display: block;
            margin-bottom: 5px;
            font-size: 16px;
        }
        
        .rejected-message p {
            margin: 0 0 10px 0;
            font-size: 14px;
            line-height: 1.4;
        }
        
        .rejection-details {
            background-color: #ffebee;
            border-left: 3px solid #d32f2f;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        
        .rejection-details p {
            margin: 5px 0;
        }
        
        .rejection-action {
            margin-top: 15px !important;
            font-weight: 500;
        }
        
        .pending-message {
            display: flex;
            align-items: flex-start;
            gap: 10px;
            padding: 5px;
            background-color: #fff8e1;
            border-radius: 4px;
        }
        
        .pending-message i {
            font-size: 24px;
            color: #f57c00;
        }
        
        .pending-message strong {
            display: block;
            margin-bottom: 5px;
            font-size: 16px;
            color: #e65100;
        }
        
        .pending-message p {
            margin: 0;
            font-size: 14px;
            line-height: 1.4;
            color: #5d4037;
        }
        
        @media (max-width: 768px) {
            .login-container {
                flex-direction: column;
                height: auto;
            }
            
            .welcome-panel, .login-panel {
                width: 100%;
            }
            
            .welcome-panel {
                padding: 30px 20px;
                height: 200px;
            }
            
            .welcome-image {
                display: none;
            }
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="welcome-panel">
            <div class="welcome-image">
                <img src="images/paranaque/logo.png" alt="Parañaque City Logo" style="width: 200px; height: 180px;">
            </div>
            <div class="system-intro">CITY OF PARAÑAQUE<br>BARANGAY HEALTH CENTER SYSTEM</div>
        </div>
        
        <div class="login-panel">
            <div class="login-header">
                <h1 class="login-title">Login to your Account</h1>
            </div>
            
            <?php if (isset($_SESSION['error'])): ?>
                <div class="<?php echo (isset($_SESSION['pending']) && $_SESSION['pending']) ? 'alert-pending' : 'alert alert-danger'; ?>">
                    <?php if (isset($_SESSION['rejected']) && $_SESSION['rejected']): ?>
                        <div class="rejected-message">
                            <i class="fas fa-exclamation-circle"></i>
                            <div>
                                <strong>Account Rejected</strong>
                                <p><?php echo isset($_SESSION['rejected_message']) ? $_SESSION['rejected_message'] : "Your registration has been rejected by an administrator."; ?></p>
                                <p class="rejection-action">Please <a href="register.php" class="register-link">create a new account</a> or contact support for assistance.</p>
                            </div>
                        </div>
                    <?php elseif (isset($_SESSION['pending']) && $_SESSION['pending']): ?>
                        <div class="pending-message">
                            <i class="fas fa-user-clock"></i>
                            <div>
                                <strong><?php echo $_SESSION['error']; ?></strong>
                                <p><?php echo $_SESSION['pending_message']; ?></p>
                            </div>
                        </div>
                    <?php else: ?>
                        <?php echo $_SESSION['error']; ?>
                    <?php endif; ?>
                </div>
                <?php 
                // Unset all notification-related session variables
                unset($_SESSION['error']); 
                unset($_SESSION['rejected']); 
                unset($_SESSION['rejected_message']);
                unset($_SESSION['rejection_reason']);
                unset($_SESSION['rejection_date']);
                unset($_SESSION['pending']);
                unset($_SESSION['pending_message']);
                ?>
            <?php endif; ?>
            
            <?php if(isset($_SESSION['success'])): ?>
            <div class="success-message">
                <?php echo $_SESSION['success']; unset($_SESSION['success']); ?>
                <?php if(isset($_SESSION['success']) && strpos($_SESSION['success'], 'pending approval') !== false): ?>
                <div class="pending-notice">
                    <i class="fas fa-info-circle"></i>
                    <p>Your account will be reviewed by an administrator. You will be notified when your account is approved.</p>
                </div>
                <?php endif; ?>
            </div>
            <?php endif; ?>
            
            <form action="process_login.php" method="post">
                <div class="form-group">
                    <label for="username">Username</label>
                    <div class="input-group">
                        <i class="fas fa-user"></i>
                        <input type="text" id="username" name="username" placeholder="Enter your username" required>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="password">Password</label>
                    <div class="input-group">
                        <i class="fas fa-lock"></i>
                        <input type="password" id="password" name="password" placeholder="Enter your password" required>
                        <i class="fas fa-eye toggle-password"></i>
                    </div>
                </div>
                
                <button type="submit" class="login-btn">Login</button>
                
                <div class="login-footer">
                    <span>Don't have an account?</span>
                    <a href="register.php" class="register-link">Register</a>
                </div>
            </form>
        </div>
    </div>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const togglePassword = document.querySelector('.toggle-password');
            const passwordField = document.querySelector('#password');
            
            togglePassword.addEventListener('click', function() {
                const type = passwordField.getAttribute('type') === 'password' ? 'text' : 'password';
                passwordField.setAttribute('type', type);
                this.classList.toggle('fa-eye');
                this.classList.toggle('fa-eye-slash');
            });
        });
    </script>
</body>
</html> 