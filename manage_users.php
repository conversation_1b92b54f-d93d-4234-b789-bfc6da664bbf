<?php
session_start();
include 'config.php';

// Enable error reporting for troubleshooting
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header("Location: login.php");
    exit();
}

// Get current user information
    $user_id = $_SESSION['user_id'];
$fetch_user = "SELECT * FROM users WHERE id = ?";
$stmt = $conn->prepare($fetch_user);
    $stmt->bind_param("i", $user_id);
    $stmt->execute();
    $result = $stmt->get_result();
    $current_user = $result->fetch_assoc();
$stmt->close();
    
// Only super_admin and admin can access this page
if ($current_user['role'] != 'super_admin' && $current_user['role'] != 'admin') {
        header("Location: dashboard.php");
        exit();
}

$username = $_SESSION['username'];

// Handle user status updates
if(isset($_POST['action']) && isset($_POST['user_id'])) {
    $action = $_POST['action'];
    $target_user_id = $_POST['user_id'];
    
    // Get the target user's role
    $check_role = "SELECT role FROM users WHERE id = ?";
    $stmt = $conn->prepare($check_role);
    $stmt->bind_param("i", $target_user_id);
    $stmt->execute();
    $result = $stmt->get_result();
    $target_user = $result->fetch_assoc();
    
    // Only super_admin can modify another super_admin
    if($target_user['role'] === 'super_admin' && $current_user['role'] !== 'super_admin') {
        $_SESSION['error'] = "You don't have permission to modify a Super Admin account.";
    } else {
        if($action === 'activate') {
            $sql = "UPDATE users SET status = 'active' WHERE id = ?";
        } else if($action === 'deactivate') {
            $sql = "UPDATE users SET status = 'inactive' WHERE id = ?";
        } else if($action === 'delete') {
            $sql = "DELETE FROM users WHERE id = ?";
        }
        
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("i", $target_user_id);
        $stmt->execute();
        $stmt->close();
        
        if($action === 'activate') {
            $_SESSION['success'] = "User activated successfully.";
        } else if($action === 'deactivate') {
            $_SESSION['success'] = "User deactivated successfully.";
        } else if($action === 'delete') {
            $_SESSION['success'] = "User deleted successfully.";
        }
    }
}

// Check if form is submitted for user actions
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && isset($_POST['user_id'])) {
    $action = $_POST['action'];
    $userId = $_POST['user_id'];

    // Make sure current user is not performing action on themselves
    if ($userId == $_SESSION['user_id'] && ($action == 'deactivate' || $action == 'delete')) {
        // Cannot deactivate or delete yourself
    } else {
        if ($action === 'activate') {
            $sql = "UPDATE users SET status = 'active' WHERE id = ?";
            $stmt = $conn->prepare($sql);
            $stmt->bind_param("i", $userId);
            $stmt->execute();
            $stmt->close();
        } elseif ($action === 'deactivate') {
            $sql = "UPDATE users SET status = 'inactive' WHERE id = ?";
            $stmt = $conn->prepare($sql);
            $stmt->bind_param("i", $userId);
            $stmt->execute();
            $stmt->close();
        } elseif ($action === 'delete') {
            // Only allow deletion if user is not a super_admin
            $check_sql = "SELECT role FROM users WHERE id = ?";
            $check_stmt = $conn->prepare($check_sql);
            $check_stmt->bind_param("i", $userId);
            $check_stmt->execute();
            $result = $check_stmt->get_result();
            $user = $result->fetch_assoc();
            $check_stmt->close();

            if ($user && $user['role'] !== 'super_admin') {
                $sql = "DELETE FROM users WHERE id = ?";
                $stmt = $conn->prepare($sql);
                $stmt->bind_param("i", $userId);
                $stmt->execute();
                $stmt->close();
            }
        }
    }

    // Redirect to prevent form resubmission
    header("Location: manage_users.php");
    exit();
}

// Count pending users
$pending_count_sql = "SELECT COUNT(*) as count FROM users WHERE status = 'pending'";
$pending_count_result = $conn->query($pending_count_sql);
$pending_count = $pending_count_result->fetch_assoc()['count'];

// Count rejected users
$rejected_count_sql = "SELECT COUNT(*) as count FROM rejected_users";
$rejected_count_result = $conn->query($rejected_count_sql);
$rejected_count = $rejected_count_result->fetch_assoc()['count'];

// Get all users
$sql = "SELECT * FROM users ORDER BY 
    CASE 
        WHEN role = 'super_admin' THEN 1
        WHEN role = 'admin' THEN 2
        WHEN role = 'staff' THEN 3
    END,
    username ASC";
$result = $conn->query($sql);
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>User Management</title>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: 'Poppins', sans-serif;
            background: linear-gradient(135deg, #e8f5e9 0%, #c8e6c9 100%);
            margin: 0;
            padding: 0;
            min-height: 100vh;
        }

        /* Topbar Styles */
        .topbar {
            background: white;
            padding: 12px 20px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            display: flex;
            justify-content: space-between;
            align-items: center;
            height: 64px;
        }

        .logo-section {
            display: flex;
            align-items: center;
            height: 100%;
        }

        .logo {
            color: #2e7d32;
            font-size: 20px;
            font-weight: 600;
            display: flex;
            align-items: center;
            text-decoration: none;
            height: 100%;
            padding-right: 20px;
            margin-right: 20px;
            border-right: 1px solid #f0f0f0;
        }

        .logo-image {
            height: 40px;
            width: auto;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 3px;
        }

        .nav-menu {
            display: flex;
            align-items: center;
            height: 100%;
        }

        .nav-item {
            display: flex;
            align-items: center;
            padding: 0 15px;
            color: #666;
            text-decoration: none;
            border-radius: 6px;
            transition: all 0.2s ease;
            font-size: 14px;
            height: 40px;
        }

        .nav-item:hover {
            background: #f0f7f0;
            color: #2e7d32;
        }

        .nav-item.active {
            background: #e8f5e9;
            color: #2e7d32;
            font-weight: 500;
        }

        .nav-item i {
            margin-right: 8px;
            width: 16px;
            text-align: center;
            font-size: 14px;
        }

        /* Main Content Styles */
        .main-content {
            margin-top: 60px;
            padding: 20px;
            width: 100%;
            box-sizing: border-box;
        }

        .header {
            background: transparent;
            padding: 0 0 15px 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        }

        .user-profile {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .user-profile span {
            padding: 6px 12px;
            background: white;
            border-radius: 4px;
            color: #2e7d32;
            font-size: 13px;
            font-weight: 500;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
        }

        .logout-btn {
            padding: 6px 12px;
            background: #ffebee;
            border-radius: 4px;
            color: #dc3545;
            text-decoration: none;
            font-size: 13px;
            font-weight: 500;
            transition: all 0.2s ease;
        }

        .logout-btn:hover {
            background: #dc3545;
            color: white;
        }

        /* Add User Button Styles */
        .add-user-btn {
            display: none;
        }

        .pending-approvals-btn {
            position: sticky;
            top: 70px;
            z-index: 900;
            margin: 0 20px 20px;
            background: #fff;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            color: #f57c00;
            padding: 12px 24px;
            border-radius: 6px;
            text-decoration: none;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.2s ease;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
            border: 1px solid #fff3e0;
            cursor: pointer;
        }

        .rejected-users-btn {
            position: sticky;
            top: 70px;
            z-index: 900;
            margin: 0 20px 20px;
            background: #fff;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            color: #d32f2f;
            padding: 12px 24px;
            border-radius: 6px;
            text-decoration: none;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.2s ease;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
            border: 1px solid #ffebee;
            cursor: pointer;
        }

        .new-user-btn {
            position: sticky;
            top: 70px;
            z-index: 900;
            margin: 0 20px 20px;
            background: #fff;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            color: #2e7d32;
            padding: 12px 24px;
            border-radius: 6px;
            text-decoration: none;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.2s ease;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
            border: 1px solid #e8f5e9;
            cursor: pointer;
        }

        .pending-approvals-btn:hover {
            background: #fff3e0;
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(245, 124, 0, 0.1);
        }

        .rejected-users-btn:hover {
            background: #ffebee;
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(211, 47, 47, 0.1);
        }

        .new-user-btn:hover {
            background: #e8f5e9;
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(46, 125, 50, 0.1);
        }

        .pending-count {
            background: #f57c00;
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 600;
        }

        .table-container {
            background: white;
            padding: 20px;
            margin: 20px;
            border-radius: 8px;
            border: 1px solid rgba(0, 0, 0, 0.05);
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.02);
        }

        .users-table {
            width: 100%;
            border-collapse: separate;
            border-spacing: 0;
            margin-top: 15px;
            text-transform: uppercase;
        }

        .users-table th {
            background: #47a84e;
            color: white;
            padding: 12px 15px;
            text-align: left;
            font-weight: 500;
            font-size: 13px;
            border-left: 1px solid rgba(255, 255, 255, 0.2);
        }

        .users-table th:first-child {
            border-left: none;
            border-top-left-radius: 6px;
        }

        .users-table th:last-child {
            border-top-right-radius: 6px;
        }

        .users-table td {
            padding: 12px 15px;
            border-bottom: 1px solid #eee;
            border-left: 1px solid #eee;
            font-size: 13px;
            color: #444;
        }

        .users-table td:first-child {
            border-left: none;
        }

        .users-table tr:hover {
            background-color: #f8faf8;
        }

        .users-table tr:last-child td:first-child {
            border-bottom-left-radius: 6px;
        }

        .users-table tr:last-child td:last-child {
            border-bottom-right-radius: 6px;
        }

        .status-badge {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 13px;
            font-weight: 500;
        }

        .status-active {
            background-color: #e8f5e9;
            color: #2e7d32;
        }

        .status-inactive {
            background-color: #ffebee;
            color: #dc3545;
        }

        .action-btn {
            width: 36px;
            height: 36px;
            border-radius: 4px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            color: white;
            cursor: pointer;
            border: none;
            text-decoration: none;
            transition: all 0.2s;
        }

        .edit-btn {
            background: linear-gradient(135deg, #2e7d32 0%, #388e3c 100%);
        }

        .activate-btn {
            background: linear-gradient(135deg, #2e7d32 0%, #388e3c 100%);
        }

        .deactivate-btn {
            background: #dc3545;
        }

        .delete-btn {
            background: #dc3545;
        }

        .activity-btn {
            background: linear-gradient(135deg, #2e7d32 0%, #388e3c 100%);
        }

        .action-btn:hover {
            opacity: 0.9;
            transform: translateY(-1px);
        }

        .success-message, .error-message {
            margin: 20px;
        }

        h2 {
            color: #333;
            font-size: 14px;
            font-weight: 500;
            margin: 20px 0 15px;
        }

        h2:first-of-type {
            margin-top: 0;
        }

        .section-divider {
            margin: 40px 0;
            border: none;
            border-top: 1px solid #e0e0e0;
        }

        /* Table Styles */
        .table-container {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
            margin-top: 20px;
        }

        .pending-users-section {
            scroll-margin-top: 80px;
            background: #fff;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
            border: 1px solid rgba(0, 0, 0, 0.05);
            transition: all 0.3s ease;
        }

        .pending-badge {
            display: inline-flex;
            background: #f57c00;
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 0.8rem;
            margin-left: 8px;
        }

        .pending-badge i {
            font-size: 10px;
            margin-right: 4px;
        }

        .section-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
        }

        .section-title {
            font-size: 16px;
            color: #2e7d32;
            font-weight: 500;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .section-title i {
            font-size: 16px;
        }

        .approve-btn {
            padding: 4px 12px;
            background: #e8f5e9;
            color: #2e7d32;
            border: none;
            border-radius: 4px;
            font-size: 13px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .approve-btn:hover {
            background: #2e7d32;
            color: white;
        }

        .reject-btn {
            padding: 4px 12px;
            background: #ffebee;
            color: #dc3545;
            border: none;
            border-radius: 4px;
            font-size: 13px;
            cursor: pointer;
            transition: all 0.2s ease;
            margin-left: 5px;
        }

        .reject-btn:hover {
            background: #dc3545;
            color: white;
        }

        /* Scroll Offset Styles */
        html {
            scroll-padding-top: 80px;
            scroll-behavior: smooth;
        }

        /* Add highlight animation */
        @keyframes highlight {
            0% { background-color: #fff3e0; }
            50% { background-color: #fff; }
            100% { background-color: #fff3e0; }
        }

        /* Add these CSS styles */
        .alert {
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 4px;
        }

        .alert-success {
            background-color: #dff0d8;
            border: 1px solid #d6e9c6;
            color: #3c763d;
        }

        .alert-danger {
            background-color: #f2dede;
            border: 1px solid #ebccd1;
            color: #a94442;
        }

        .pending-users-section {
            background: #fff;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            border: 1px solid #f0f0f0;
        }

        .section-header {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
        }

        .section-title {
            font-size: 1.2rem;
            color: #333;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .section-title i {
            color: #f57c00;
        }

        .pending-badge {
            display: inline-flex;
            background: #f57c00;
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 0.8rem;
            margin-left: 8px;
        }

        .action-buttons {
            display: flex;
            gap: 8px;
        }

        .approve-btn {
            background: #4caf50;
            color: white;
            border: none;
            padding: 6px 12px;
            border-radius: 4px;
            cursor: pointer;
            display: inline-flex;
            align-items: center;
            gap: 4px;
            font-size: 0.9rem;
        }

        .reject-btn {
            background: #f44336;
            color: white;
            border: none;
            padding: 6px 12px;
            border-radius: 4px;
            cursor: pointer;
            display: inline-flex;
            align-items: center;
            gap: 4px;
            font-size: 0.9rem;
        }

        .approve-btn:hover {
            background: #388e3c;
        }

        .reject-btn:hover {
            background: #d32f2f;
        }

        .no-records-container {
            padding: 30px;
            text-align: center;
        }

        .no-records {
            text-align: center;
            color: #666;
            font-style: italic;
        }

        /* Highlight animation */
        @keyframes highlightSection {
            0% { background-color: #fff; }
            50% { background-color: #fff3e0; }
            100% { background-color: #fff; }
        }

        .highlight {
            animation: highlightSection 2s ease;
        }

        .status-badge {
            display: inline-block;
            padding: 3px 8px;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: 500;
        }

        .status-active {
            background-color: #e8f5e9;
            color: #2e7d32;
        }

        .status-pending {
            background-color: #fff3e0;
            color: #f57c00;
        }

        .status-inactive {
            background-color: #f5f5f5;
            color: #757575;
        }

        /* Add action button styles */
        .action-buttons {
            display: flex;
            gap: 5px;
            justify-content: center;
        }

        .action-btn {
            width: 36px;
            height: 36px;
            border-radius: 4px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            color: white;
            cursor: pointer;
            border: none;
            text-decoration: none;
            transition: all 0.2s;
        }

        .action-btn i {
            font-size: 16px;
            width: 16px;
            height: 16px;
            line-height: 16px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
        }

        .edit-btn {
            background-color: #2196F3;
        }

        .status-btn {
            background-color: #F44336;
        }

        .active-btn {
            background-color: #4CAF50;
        }

        .activity-btn {
            background-color: #FF9800;
        }

        .delete-btn {
            background-color: #9E9E9E;
        }

        .action-btn:hover {
            opacity: 0.85;
            transform: scale(1.05);
        }

        .no-actions {
            color: #666;
            font-style: italic;
            font-size: 12px;
        }

        /* Pending section initially hidden */
        #pending-section {
            display: none;
        }

        /* Add highlight animation */
        @keyframes highlightSection {
            0% { background-color: #fff; }
            50% { background-color: #fff3e0; }
            100% { background-color: #fff; }
        }

        .highlight {
            animation: highlightSection 2s ease;
        }

        .rejected-count {
            display: inline-flex;
            background: #d32f2f;
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 0.8rem;
            margin-left: 8px;
        }

        .rejected-badge {
            display: inline-flex;
            background: #d32f2f;
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 0.8rem;
            margin-left: 8px;
        }

    </style>
</head>
<body>
    <!-- Topbar -->
    <div class="topbar">
        <div class="logo-section">

            <nav class="nav-menu">
            <a href="dashboard.php" class="nav-item">
                <i class="fas fa-home"></i>
                Dashboard
            </a>
            <?php if($current_user['role'] === 'super_admin' || $current_user['role'] === 'admin' || $current_user['role'] === 'staff'): ?>
            <a href="add_patient.php" class="nav-item">
                <i class="fas fa-user-plus"></i>
                Add Patient
            </a>
            <?php endif; ?>
            <a href="view_patients.php" class="nav-item">
                <i class="fas fa-users"></i>
                Patient Records
            </a>
            <?php if($current_user['role'] !== 'staff'): ?>
            <a href="manage_users.php" class="nav-item active">
                <i class="fas fa-users-cog"></i>
                User Management
            </a>
            <?php endif; ?>
            <a href="logout.php" class="nav-item">
                <i class="fas fa-sign-out-alt"></i>
                Logout
            </a>
        </nav>
    </div>
            <div class="user-profile">
                <span><?php echo htmlspecialchars($username); ?></span>
            </div>
        </div>

    <!-- Main Content -->
    <div class="main-content">
                <?php 
        // Add debug output
        echo "<!-- Debug: Current user role: " . $current_user['role'] . " -->";

        // Count pending users
        $pending_count_sql = "SELECT COUNT(*) as count FROM users WHERE status = 'pending'";
        $pending_count_result = $conn->query($pending_count_sql);
        $pending_count = $pending_count_result->fetch_assoc()['count'];

        echo "<!-- Debug: Pending users count: " . $pending_count . " -->";
        ?>
        <!-- Action Buttons -->
        <div style="display: flex; gap: 10px; margin-bottom: 20px;">
            <a href="#pending-section" class="pending-approvals-btn">
                <i class="fas fa-user-clock"></i>
                Pending Approvals
                <?php if($pending_count > 0): ?>
                <span class="pending-count"><?php echo $pending_count; ?></span>
                <?php endif; ?>
            </a>
            <a href="#rejected-section" class="rejected-users-btn">
                <i class="fas fa-user-times"></i>
                Rejected Users
                <?php if($rejected_count > 0): ?>
                <span class="rejected-count"><?php echo $rejected_count; ?></span>
                <?php endif; ?>
            </a>
            </div>

        <?php /* Removing the success message display
        if(isset($_SESSION['success'])): ?>
        <div class="alert alert-success">
            <?php echo $_SESSION['success']; unset($_SESSION['success']); ?>
            </div>
        <?php endif; */ ?>

        <!-- Pending Approvals Section -->
        <div id="pending-section" class="table-container">
            <div class="section-header">
                <h2 class="section-title">
                    <i class="fas fa-user-clock"></i>
                    Pending User Approvals
                    <?php if($pending_count > 0): ?>
                    <span class="pending-badge"><?php echo $pending_count; ?></span>
        <?php endif; ?>
                </h2>
            </div>

            <?php if($pending_count > 0): ?>
            <table class="users-table">
                <thead>
                    <tr>
                        <th>Username</th>
                        <th>Full Name</th>
                        <th>Designation</th>
                        <th>Facility</th>
                        <th>Registration Date</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                <?php 
                    // Fetch pending users
                    $pending_sql = "SELECT * FROM users WHERE status = 'pending' ORDER BY created_at DESC";
                    $pending_result = $conn->query($pending_sql);

                    // Debug output
                    echo "<!-- Debug: Pending users query: " . $pending_sql . " -->";
                    echo "<!-- Debug: Pending users result rows: " . $pending_result->num_rows . " -->";

                    while($row = $pending_result->fetch_assoc()) {
                        echo "<tr>";
                        echo "<td>" . htmlspecialchars($row['username']) . "</td>";
                        echo "<td>" . htmlspecialchars($row['fullname']) . "</td>";
                        echo "<td>" . htmlspecialchars($row['designation']) . "</td>";
                        echo "<td>" . htmlspecialchars($row['barangay']) . "</td>";
                        echo "<td>" . date('M d, Y', strtotime($row['created_at'])) . "</td>";
                        echo "<td class='action-buttons'>";

                        // Only show approval buttons for super_admin users
                        if($current_user['role'] === 'super_admin') {
                            echo "<button onclick=\"approveUser(" . $row['id'] . ")\" class='approve-btn'>
                                    <i class='fas fa-check'></i> Approve
                                </button>";
                            echo "<button onclick=\"rejectUser(" . $row['id'] . ")\" class='reject-btn'>
                                    <i class='fas fa-times'></i> Reject
                                </button>";
                        } else {
                            echo "<span class='no-actions'>Pending Super Admin Approval</span>";
                        }

                        echo "</td>";
                        echo "</tr>";
                    }
                    ?>
                </tbody>
            </table>
            <?php else: ?>
            <div class="no-records-container">
                <p class="no-records">No pending approvals</p>
            </div>
        <?php endif; ?>
        </div>

        <style>
            .pending-users-section {
                background: #fff;
                border-radius: 8px;
                padding: 20px;
                margin: 20px 0;
                box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
                border: 1px solid #f0f0f0;
            }

            .section-header {
                display: flex;
                align-items: center;
                margin-bottom: 20px;
                padding-bottom: 10px;
                border-bottom: 1px solid #eee;
            }

            .section-title {
                font-size: 1.2rem;
                color: #333;
                font-weight: 600;
                display: flex;
                align-items: center;
                gap: 8px;
            }

            .section-title i {
                color: #f57c00;
            }

            .pending-badge {
                display: inline-flex;
                background: #f57c00;
                color: white;
                padding: 2px 8px;
                border-radius: 12px;
                font-size: 0.8rem;
                margin-left: 8px;
            }

            .action-buttons {
                display: flex;
                gap: 8px;
            }

            .approve-btn {
                background: #4caf50;
                color: white;
                border: none;
                padding: 6px 12px;
                border-radius: 4px;
                cursor: pointer;
                display: inline-flex;
                align-items: center;
                gap: 4px;
                font-size: 0.9rem;
            }

            .reject-btn {
                background: #f44336;
                color: white;
                border: none;
                padding: 6px 12px;
                border-radius: 4px;
                cursor: pointer;
                display: inline-flex;
                align-items: center;
                gap: 4px;
                font-size: 0.9rem;
            }

            .approve-btn:hover {
                background: #388e3c;
            }

            .reject-btn:hover {
                background: #d32f2f;
            }

            .no-records-container {
                padding: 30px;
                text-align: center;
            }

            .no-records {
                text-align: center;
                color: #666;
                font-style: italic;
            }
        </style>

        <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Handle pending approvals button click
            const pendingBtn = document.querySelector('.pending-approvals-btn');
            const pendingSection = document.getElementById('pending-section');
            const rejectedBtn = document.querySelector('.rejected-users-btn');
            const rejectedSection = document.getElementById('rejected-section');

            if (pendingBtn && pendingSection) {
                pendingBtn.addEventListener('click', function(e) {
                    e.preventDefault();

                    // Hide rejected section when showing pending
                    if (rejectedSection) {
                        rejectedSection.style.display = 'none';
                    }

                    // Toggle display - if shown then hide, if hidden then show
                    if (pendingSection.style.display === 'block') {
                        pendingSection.style.display = 'none';
                    } else {
                        pendingSection.style.display = 'block';

                        // Calculate position to scroll to
                        const offset = 80; // Header height + some padding
                        const sectionPosition = pendingSection.getBoundingClientRect().top + window.pageYOffset;
                        window.scrollTo({
                            top: sectionPosition - offset,
                            behavior: 'smooth'
                        });

                        // Add highlight effect
                        pendingSection.classList.add('highlight');
                        setTimeout(() => {
                            pendingSection.classList.remove('highlight');
                        }, 2000);
                    }
                });
            }

            // Handle rejected users button click
            if (rejectedBtn && rejectedSection) {
                rejectedBtn.addEventListener('click', function(e) {
                    e.preventDefault();

                    // Hide pending section when showing rejected
                    if (pendingSection) {
                        pendingSection.style.display = 'none';
                    }

                    // Toggle display - if shown then hide, if hidden then show
                    if (rejectedSection.style.display === 'block') {
                        rejectedSection.style.display = 'none';
                    } else {
                        rejectedSection.style.display = 'block';

                        // Calculate position to scroll to
                        const offset = 80; // Header height + some padding
                        const sectionPosition = rejectedSection.getBoundingClientRect().top + window.pageYOffset;
                        window.scrollTo({
                            top: sectionPosition - offset,
                            behavior: 'smooth'
                        });

                        // Add highlight effect
                        rejectedSection.classList.add('highlight');
                        setTimeout(() => {
                            rejectedSection.classList.remove('highlight');
                        }, 2000);
                    }
                });
            }
        });

        function approveUser(userId) {
            if (confirm('Are you sure you want to approve this user?')) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.action = 'process_user_approval.php';

                const actionInput = document.createElement('input');
                actionInput.type = 'hidden';
                actionInput.name = 'action';
                actionInput.value = 'approve';

                const userIdInput = document.createElement('input');
                userIdInput.type = 'hidden';
                userIdInput.name = 'user_id';
                userIdInput.value = userId;

                form.appendChild(actionInput);
                form.appendChild(userIdInput);
                document.body.appendChild(form);
                form.submit();
            }
        }

        function rejectUser(userId) {
            if (confirm('Are you sure you want to reject this user? This action cannot be undone.')) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.action = 'process_user_approval.php';

                const actionInput = document.createElement('input');
                actionInput.type = 'hidden';
                actionInput.name = 'action';
                actionInput.value = 'reject';

                const userIdInput = document.createElement('input');
                userIdInput.type = 'hidden';
                userIdInput.name = 'user_id';
                userIdInput.value = userId;

                form.appendChild(actionInput);
                form.appendChild(userIdInput);
                document.body.appendChild(form);
                form.submit();
            }
        }
        </script>

        <div class="table-container">
            <div class="section-title">
                <i class="fas fa-user-shield"></i>
                Super Admin Users
            </div>
            <table class="users-table">
                <thead>
                    <tr>
                        <th>Username</th>
                        <th>Full Name</th>
                        <th>Designation</th>
                        <th>Status</th>
                        <th>Created At</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php 
                    // Fetch super_admin users
                    $sql = "SELECT * FROM users WHERE status != 'pending' AND role = 'super_admin' ORDER BY username ASC";
                    $result = $conn->query($sql);

                    while($row = $result->fetch_assoc()) {
                        echo "<tr>";
                        echo "<td>" . htmlspecialchars($row['username']) . "</td>";
                        echo "<td>" . htmlspecialchars($row['fullname']) . "</td>";
                        echo "<td>" . htmlspecialchars($row['designation']) . "</td>";
                        echo "<td>
                            <span class='status-badge status-" . strtolower($row['status']) . "'>
                                " . ucfirst(htmlspecialchars($row['status'])) . "
                                    </span>
                        </td>";
                        echo "<td>" . date('M d, Y', strtotime($row['created_at'])) . "</td>";
                        echo "<td class='action-buttons'>";

                        // Hide all action buttons for super_admin users if current user is admin
                        if($current_user['role'] === 'admin') {
                            echo "<span class='no-actions'>No Actions</span>";
                        } else {
                            // Edit button
                            echo "<a href='edit_user.php?id=" . $row['id'] . "' class='action-btn edit-btn' title='Edit User'>
                                <i class='fas fa-edit'></i>
                            </a>";

                            // Status toggle button (only if not current user)
                            if($row['id'] != $_SESSION['user_id']) {
                                if ($row['status'] === 'active') {
                                    echo "<form method='POST' style='display: inline;'>
                                        <input type='hidden' name='action' value='deactivate'>
                                        <input type='hidden' name='user_id' value='" . $row['id'] . "'>
                                        <button type='submit' class='action-btn status-btn' title='Deactivate User' onclick='return confirm(\"Are you sure you want to deactivate this user?\")'>
                                            <i class='fas fa-ban'></i>
                                                    </button>
                                    </form>";
                                } else {
                                    echo "<form method='POST' style='display: inline;'>
                                        <input type='hidden' name='action' value='activate'>
                                        <input type='hidden' name='user_id' value='" . $row['id'] . "'>
                                        <button type='submit' class='action-btn active-btn' title='Activate User' onclick='return confirm(\"Are you sure you want to activate this user?\")'>
                                            <i class='fas fa-check'></i>
                                                    </button>
                                    </form>";
                                }
                            }

                            // Activity log button
                            echo "<a href='user_activity.php?id=" . $row['id'] . "' class='action-btn activity-btn' title='View Activity'>
                                <i class='fas fa-history'></i>
                            </a>";
                        }

                        echo "</td>";
                        echo "</tr>";
                    }
                    ?>
                </tbody>
            </table>
        </div>

        <div class="table-container">
            <div class="section-title">
                <i class="fas fa-users"></i>
                Regular Users
            </div>
            <table class="users-table">
                <thead>
                    <tr>
                        <th>Username</th>
                        <th>Full Name</th>
                        <th>Designation</th>
                        <th>Role</th>
                        <th>Facility</th>
                        <th>Status</th>
                        <th>Created At</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php 
                    // Fetch all users EXCEPT pending users and super_admin
                    $sql = "SELECT * FROM users WHERE status != 'pending' AND role != 'super_admin' ORDER BY
                        CASE
                            WHEN role = 'admin' THEN 1
                            WHEN role = 'staff' THEN 2
                        END,
                        username ASC";
                    $result = $conn->query($sql);

                    while($row = $result->fetch_assoc()) {
                        echo "<tr>";
                        echo "<td>" . htmlspecialchars($row['username']) . "</td>";
                        echo "<td>" . htmlspecialchars($row['fullname']) . "</td>";
                        echo "<td>" . htmlspecialchars($row['designation']) . "</td>";
                        echo "<td>" . ucfirst(htmlspecialchars($row['role'])) . "</td>";
                        echo "<td>" . htmlspecialchars($row['barangay']) . "</td>";
                        echo "<td>
                            <span class='status-badge status-" . strtolower($row['status']) . "'>
                                " . ucfirst(htmlspecialchars($row['status'])) . "
                                    </span>
                        </td>";
                        echo "<td>" . date('M d, Y', strtotime($row['created_at'])) . "</td>";
                        echo "<td class='action-buttons'>";

                        // Edit button
                        echo "<a href='edit_user.php?id=" . $row['id'] . "' class='action-btn edit-btn' title='Edit User'>
                            <i class='fas fa-edit'></i>
                        </a>";

                        // Status toggle button (only if not current user)
                        if($row['id'] != $_SESSION['user_id']) {
                            if ($row['status'] === 'active') {
                                echo "<form method='POST' style='display: inline;'>
                                    <input type='hidden' name='action' value='deactivate'>
                                    <input type='hidden' name='user_id' value='" . $row['id'] . "'>
                                    <button type='submit' class='action-btn status-btn' title='Deactivate User' onclick='return confirm(\"Are you sure you want to deactivate this user?\")'>
                                        <i class='fas fa-ban'></i>
                                                </button>
                                </form>";
                            } else {
                                echo "<form method='POST' style='display: inline;'>
                                    <input type='hidden' name='action' value='activate'>
                                    <input type='hidden' name='user_id' value='" . $row['id'] . "'>
                                    <button type='submit' class='action-btn active-btn' title='Activate User' onclick='return confirm(\"Are you sure you want to activate this user?\")'>
                                        <i class='fas fa-check'></i>
                                                </button>
                                </form>";
                            }
                        }

                        // Activity log button
                        echo "<a href='user_activity.php?id=" . $row['id'] . "' class='action-btn activity-btn' title='View Activity'>
                            <i class='fas fa-history'></i>
                        </a>";

                        // Delete button (only if not current user)
                        if($row['id'] != $_SESSION['user_id']) {
                            echo "<form method='POST' style='display: inline;'>
                                <input type='hidden' name='action' value='delete'>
                                <input type='hidden' name='user_id' value='" . $row['id'] . "'>
                                <button type='submit' class='action-btn delete-btn' title='Delete User' onclick='return confirm(\"Are you sure you want to delete this user? This action cannot be undone.\")'>
                                    <i class='fas fa-trash'></i>
                                            </button>
                            </form>";
                        }

                        echo "</td>";
                        echo "</tr>";
                    }
                    ?>
                </tbody>
            </table>
        </div>
    </div>

    <!-- Rejected Users Section -->
    <div id="rejected-section" class="table-container" style="display: none;">
        <div class="section-header">
            <h2 class="section-title">
                <i class="fas fa-user-times"></i>
                Rejected Users
                <?php if($rejected_count > 0): ?>
                <span class="rejected-count"><?php echo $rejected_count; ?></span>
                <?php endif; ?>
            </h2>
        </div>

        <?php if($rejected_count > 0): ?>
            <table class="users-table">
                <thead>
                    <tr>
                        <th>Username</th>
                        <th>Full Name</th>
                        <th>Designation</th>
                    <th>Facility</th>
                    <th>Rejection Date</th>
                    <th>Rejected By</th>
                    </tr>
                </thead>
                <tbody>
                    <?php 
                // Fetch rejected users
                $rejected_sql = "SELECT r.*, u.username as rejected_by_user
                                FROM rejected_users r
                                LEFT JOIN users u ON r.rejected_by = u.id
                                ORDER BY r.rejection_date DESC";
                $rejected_result = $conn->query($rejected_sql);

                while($row = $rejected_result->fetch_assoc()) {
                    echo "<tr>";
                    echo "<td>" . htmlspecialchars($row['username']) . "</td>";
                    echo "<td>" . htmlspecialchars($row['fullname']) . "</td>";
                    echo "<td>" . htmlspecialchars($row['designation']) . "</td>";
                    echo "<td>" . htmlspecialchars($row['barangay']) . "</td>";
                    echo "<td>" . (isset($row['rejection_date']) ? date('M d, Y g:i A', strtotime($row['rejection_date'])) : 'N/A') . "</td>";
                    echo "<td>" . htmlspecialchars($row['rejected_by_user'] ?? 'Unknown') . "</td>";
                    echo "</tr>";
                }
                ?>
                </tbody>
            </table>
        <?php else: ?>
        <div class="no-records-container">
            <p class="no-records">No rejected users found</p>
        </div>
        <?php endif; ?>
    </div>

    <style>
        .rejected-users-section {
            background: #fff;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            border: 1px solid #f0f0f0;
        }

        .section-header {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
        }

        .section-title {
            font-size: 1.2rem;
            color: #333;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .section-title i {
            color: #f57c00;
        }

        .rejected-count {
            display: inline-flex;
            background: #d32f2f;
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 0.8rem;
            margin-left: 8px;
        }

        .action-buttons {
            display: flex;
            gap: 8px;
        }

        .approve-btn {
            background: #4caf50;
            color: white;
            border: none;
            padding: 6px 12px;
            border-radius: 4px;
            cursor: pointer;
            display: inline-flex;
            align-items: center;
            gap: 4px;
            font-size: 0.9rem;
        }

        .reject-btn {
            background: #f44336;
            color: white;
            border: none;
            padding: 6px 12px;
            border-radius: 4px;
            cursor: pointer;
            display: inline-flex;
            align-items: center;
            gap: 4px;
            font-size: 0.9rem;
        }

        .approve-btn:hover {
            background: #388e3c;
        }

        .reject-btn:hover {
            background: #d32f2f;
        }

        .no-records-container {
            padding: 30px;
            text-align: center;
        }

        .no-records {
            text-align: center;
            color: #666;
            font-style: italic;
        }
    </style>
</body>
</html> 