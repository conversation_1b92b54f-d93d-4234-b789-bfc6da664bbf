<?php
// Enable error reporting
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Include database connection
include 'config.php';

echo "<h1>Database Fix</h1>";

// Check if status column exists
$checkColumn = $conn->query("SHOW COLUMNS FROM users LIKE 'status'");
$statusExists = $checkColumn->num_rows > 0;

if ($statusExists) {
    echo "<p>Status column exists.</p>";
    
    // Check column type
    $columnInfo = $checkColumn->fetch_assoc();
    echo "<p>Current status column type: " . $columnInfo['Type'] . "</p>";
    
    // If the status column is not correctly defined as enum, modify it
    if ($columnInfo['Type'] != "enum('pending','active','inactive')") {
        echo "<p>Updating status column type...</p>";
        $updateQuery = "ALTER TABLE users MODIFY COLUMN status ENUM('pending', 'active', 'inactive') DEFAULT 'pending'";
        
        if ($conn->query($updateQuery)) {
            echo "<p>Status column updated successfully!</p>";
        } else {
            echo "<p>Error updating status column: " . $conn->error . "</p>";
        }
    } else {
        echo "<p>Status column is already correctly defined.</p>";
    }
} else {
    echo "<p>Status column doesn't exist. Adding it now...</p>";
    $addColumnQuery = "ALTER TABLE users ADD COLUMN status ENUM('pending', 'active', 'inactive') DEFAULT 'pending'";
    
    if ($conn->query($addColumnQuery)) {
        echo "<p>Status column added successfully!</p>";
    } else {
        echo "<p>Error adding status column: " . $conn->error . "</p>";
    }
}

// Set all users without a status to 'active'
$updateNullQuery = "UPDATE users SET status = 'active' WHERE status IS NULL OR status = ''";
if ($conn->query($updateNullQuery)) {
    echo "<p>Updated users with null status.</p>";
} else {
    echo "<p>Error updating null status: " . $conn->error . "</p>";
}

// Show current user statuses
echo "<h2>Current User Statuses</h2>";
$usersQuery = "SELECT id, username, status FROM users";
$usersResult = $conn->query($usersQuery);

echo "<table border='1'>";
echo "<tr><th>ID</th><th>Username</th><th>Status</th></tr>";

while ($row = $usersResult->fetch_assoc()) {
    echo "<tr>";
    echo "<td>" . $row['id'] . "</td>";
    echo "<td>" . $row['username'] . "</td>";
    echo "<td>" . $row['status'] . "</td>";
    echo "</tr>";
}

echo "</table>";

echo "<p>Done! <a href='manage_users.php'>Return to Manage Users</a></p>";
?> 