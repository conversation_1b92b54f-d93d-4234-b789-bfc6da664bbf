<?php
session_start();
include 'config.php';
include 'activity_logger.php';

// Check if user is logged in
if(!isset($_SESSION['user_id'])) {
    header("Location: login.php");
    exit();
}

// Get current user information
$user_id = $_SESSION['user_id'];
$check_user = "SELECT * FROM users WHERE id = ?";
$stmt = $conn->prepare($check_user);
$stmt->bind_param("i", $user_id);
$stmt->execute();
$result = $stmt->get_result();
$current_user = $result->fetch_assoc();

// Check if user is admin or super_admin
if($current_user['role'] !== 'admin' && $current_user['role'] !== 'super_admin' && $current_user['role'] !== 'staff') {
    header("Location: dashboard.php");
    exit();
}

$username = $_SESSION['username'];

// Handle form submission
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    try {
        $control_number = date('Y') . '-' . str_pad(rand(0, 9999), 4, '0', STR_PAD_LEFT);
        $fullname = $conn->real_escape_string($_POST['fullname']);
        $age = $conn->real_escape_string($_POST['age']);
        $sex = $conn->real_escape_string($_POST['sex']);
        $address = $conn->real_escape_string($_POST['address']);
        $purpose = $conn->real_escape_string($_POST['purpose']);
        $registration_date = !empty($_POST['registration_date']) ? $conn->real_escape_string($_POST['registration_date']) : date('Y-m-d');
        $barangay = $conn->real_escape_string($_POST['barangay']);
        $diagnosis = isset($_POST['diagnosis']) ? $conn->real_escape_string($_POST['diagnosis']) : '';

        // First check if diagnosis column exists
        $check_diagnosis = $conn->query("SHOW COLUMNS FROM patients LIKE 'diagnosis'");

        // If diagnosis column doesn't exist, add it
        if($check_diagnosis->num_rows == 0) {
            $add_column = "ALTER TABLE patients ADD COLUMN diagnosis TEXT NULL AFTER purpose";
            $conn->query($add_column);
        }

        // Check if the registration_date column exists
        $check_column = $conn->query("SHOW COLUMNS FROM patients LIKE 'registration_date'");

        if($check_column->num_rows > 0) {
            // Use the new schema with registration_date
            $sql = "INSERT INTO patients (control_number, fullname, age, sex, address, purpose, barangay, registration_date, diagnosis)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)";
            $stmt = $conn->prepare($sql);
            $stmt->bind_param("ssissssss", $control_number, $fullname, $age, $sex, $address, $purpose, $barangay, $registration_date, $diagnosis);
        } else {
            // Use the old schema without registration_date
            $sql = "INSERT INTO patients (control_number, fullname, age, sex, address, purpose, barangay, diagnosis)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)";
            $stmt = $conn->prepare($sql);
            $stmt->bind_param("ssisssss", $control_number, $fullname, $age, $sex, $address, $purpose, $barangay, $diagnosis);
        }

        if ($stmt->execute()) {
            $patient_id = $conn->insert_id;
            $details = "Added patient: " . $fullname . " (ID: " . $patient_id . ")";
            logActivity($conn, $_SESSION['user_id'], "Add Patient", $details);
            $_SESSION['success'] = "Patient added successfully!";
            header("Location: view_patients.php");
            exit();
        } else {
            $error_message = "Error: " . $stmt->error;
        }

        $stmt->close();
    } catch (Exception $e) {
        $error_message = "Error: " . $e->getMessage();
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Add Patient</title>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="uxintace-topbar.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Poppins', sans-serif;
        }

        body {
            font-family: 'Poppins', sans-serif;
            background: linear-gradient(135deg, #e8f5e9 0%, #c8e6c9 100%);
            margin: 0;
            padding: 0;
            min-height: 100vh;
        }

        /* Topbar Styles */
        .topbar {
            background: #2e7d32;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 20px;
            height: 50px;
        }

        .nav-menu {
            display: flex;
            align-items: center;
        }

        .nav-item {
            background: rgba(255, 255, 255, 0.15);
            color: white;
            text-decoration: none;
            padding: 8px 15px;
            border-radius: 4px;
            margin-right: 10px;
            font-size: 14px;
            display: flex;
            align-items: center;
            transition: all 0.3s ease;
        }

        .nav-item:hover {
            background: rgba(255, 255, 255, 0.25);
        }

        .nav-item.active {
            background: rgba(255, 255, 255, 0.3);
            font-weight: 500;
        }

        .nav-item i {
            margin-right: 8px;
        }

        .user-profile {
            background: rgba(255, 255, 255, 0.15);
            color: white;
            padding: 8px 15px;
            border-radius: 4px;
            font-size: 14px;
            display: flex;
            align-items: center;
        }

        .user-profile i {
            margin-right: 8px;
        }

        /* Main Content Styles */
        .main-content {
            margin-top: 70px;
            padding: 20px;
            width: 100%;
            box-sizing: border-box;
        }

        .form-container {
            background: white;
            border-radius: 8px;
            padding: 25px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
            max-width: 800px;
            margin: 0 auto;
        }

        .form-title {
            font-size: 18px;
            color: #333;
            margin-bottom: 20px;
            font-weight: 500;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-label {
            display: block;
            margin-bottom: 5px;
            color: #555;
            font-size: 13px;
        }

        .form-control {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            transition: border-color 0.2s ease;
        }

        .form-control:focus {
            border-color: #2e7d32;
            outline: none;
        }

        .btn-submit {
            background: #2e7d32;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: background 0.2s ease;
        }

        .btn-submit:hover {
            background: #1b5e20;
        }

        .success-message {
            background-color: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }

        .error-message {
            background-color: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }

        .form-text {
            font-size: 12px;
            color: #ff6b01;
            margin-top: 5px;
            display: block;
            font-weight: 500;
            padding-left: 5px;
        }

        .control-number-preview {
            background-color: #f8f9fa;
            color: #495057;
            font-family: monospace;
            letter-spacing: 1px;
        }
    </style>
</head>
<body>
    <!-- Topbar -->
    <div class="topbar">
        <div class="nav-menu">
            <a href="dashboard.php" class="nav-item">
                <i class="fas fa-home"></i> Dashboard
            </a>
            <a href="add_patient.php" class="nav-item active">
                <i class="fas fa-user-plus"></i> Add Patient
            </a>
            <a href="view_patients.php" class="nav-item">
                <i class="fas fa-users"></i> Patient Records
            </a>
            <?php if($current_user['role'] === 'super_admin' || $current_user['role'] === 'admin'): ?>
            <a href="manage_users.php" class="nav-item">
                <i class="fas fa-user-cog"></i> User Management
            </a>
            <?php endif; ?>
            <a href="logout.php" class="nav-item">
                <i class="fas fa-sign-out-alt"></i> Logout
            </a>
        </div>
        <div class="user-profile">
            <i class="fas fa-user"></i> <?php echo htmlspecialchars($username); ?>
        </div>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <div class="form-container">
            <h2 class="form-title">Add New Patient</h2>
            <?php if(isset($success_message)): ?>
                <div class="success-message"><?php echo $success_message; ?></div>
            <?php endif; ?>

            <?php if(isset($error_message)): ?>
                <div class="error-message"><?php echo $error_message; ?></div>
            <?php endif; ?>

            <form method="POST" action="">
                <div class="form-group">
                    <label for="control_number_preview" class="form-label">Control Number (Auto-generated)</label>
                    <input type="text" id="control_number_preview" class="form-control control-number-preview" value="<?php echo date('Y') . '-' . str_pad(rand(1000, 9999), 4, '0', STR_PAD_LEFT); ?>" readonly>
                    <small class="form-text">This is a preview. The actual control number will be generated upon submission.</small>
                </div>

                <div class="form-group">
                    <label for="registration_date" class="form-label">Registration Date</label>
                    <input type="date" id="registration_date" name="registration_date" class="form-control" value="<?php echo date('Y-m-d'); ?>">
                </div>

                <div class="form-group">
                    <label for="fullname" class="form-label">Full Name</label>
                    <input type="text" id="fullname" name="fullname" required placeholder="Enter patient's full name" class="form-control" pattern=".*,.*" title="Format should be: Last Name, First Name Middle Name">
                    <small class="form-text">Format: Last Name, First Name Middle Name. Example: Dela Cruz, Juan Martinez</small>
                </div>

                <div class="form-group">
                    <label for="age" class="form-label">Age</label>
                    <input type="number" id="age" name="age" min="0" max="150" required placeholder="Enter age" class="form-control">
                </div>

                <div class="form-group">
                    <label for="sex" class="form-label">Sex</label>
                    <select id="sex" name="sex" required class="form-control">
                        <option value="">Select Sex</option>
                        <option value="Male">Male</option>
                        <option value="Female">Female</option>
                        <option value="Other">Other</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="address" class="form-label">Address</label>
                    <textarea id="address" name="address" required placeholder="Enter complete address" class="form-control"></textarea>
                </div>

                <div class="form-group">
                    <label for="diagnosis" class="form-label">Diagnosis</label>
                    <textarea id="diagnosis" name="diagnosis" placeholder="Enter patient diagnosis (if applicable)" class="form-control"></textarea>
                </div>

                <div class="form-group">
                    <label for="purpose" class="form-label">Purpose</label>
                    <textarea id="purpose" name="purpose" required placeholder="Enter purpose for medical certificate" class="form-control"></textarea>
                </div>

                <div class="form-group">
                    <label for="barangay" class="form-label">Facility/Barangay</label>
                    <?php if($current_user['role'] === 'super_admin'): ?>
                    <select id="barangay" name="barangay" required class="form-control">
                        <option value="">Select Barangay</option>
                        <?php
                        $barangays = [
                            'Baclaran', 'B. F. Homes', 'Don Bosco', 'Don Galo', 'La Huerta',
                            'Merville', 'Moonwalk', 'San Antonio', 'San Dionisio', 'San Isidro',
                            'San Martin de Porres', 'Marcelo Green', 'Sto. Niño', 'Sun Valley', 'Tambo',
                            'Vitalez'
                        ];
                        sort($barangays);
                        foreach($barangays as $barangay_option) {
                            echo '<option value="' . htmlspecialchars($barangay_option) . '"';
                            if($barangay_option === $current_user['barangay']) {
                                echo ' selected';
                            }
                            echo '>' . htmlspecialchars($barangay_option) . '</option>';
                        }
                        ?>
                    </select>
                    <?php else: ?>
                    <input type="text" class="form-control" value="<?php echo htmlspecialchars($current_user['barangay']); ?>" readonly>
                    <input type="hidden" name="barangay" value="<?php echo htmlspecialchars($current_user['barangay']); ?>">
                    <?php endif; ?>
                </div>

                <button type="submit" class="btn-submit">Register Patient</button>
            </form>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.querySelector('form');
            const fullnameInput = document.getElementById('fullname');

            // Add focus highlight
            fullnameInput.addEventListener('focus', function() {
                this.style.backgroundColor = '#fffde7'; // Light yellow background
            });

            // Remove highlight on blur
            fullnameInput.addEventListener('blur', function() {
                this.style.backgroundColor = '';
            });

            // Live validation as user types
            fullnameInput.addEventListener('input', function() {
                const fullname = this.value.trim();
                if (fullname === '') {
                    // No validation for empty input
                    this.style.borderColor = '';
                } else if (fullname.includes(',')) {
                    // Valid format
                    this.style.borderColor = '#4caf50'; // Green border
                } else {
                    // Invalid format
                    this.style.borderColor = '#f44336'; // Red border
                }
            });

            // Form submission validation
            form.addEventListener('submit', function(e) {
                const fullname = fullnameInput.value.trim();

                // Check if the fullname contains a comma
                if (!fullname.includes(',')) {
                    e.preventDefault();
                    alert('Please use the correct format for Full Name: Last Name, First Name Middle Name. Example: Dela Cruz, Juan Martinez');
                    fullnameInput.focus();
                }
            });
        });
    </script>
    <script>
        // Initialize animations for the topbar
        document.addEventListener('DOMContentLoaded', function() {
            // Set all nav items to be visible
            const navItems = document.querySelectorAll('.nav-item');
            navItems.forEach(item => {
                setTimeout(() => {
                    item.style.opacity = '1';
                }, 100);
            });

            // Set user profile to be visible
            const userProfile = document.querySelector('.user-profile');
            if (userProfile) {
                setTimeout(() => {
                    userProfile.style.opacity = '1';
                }, 300);
            }
        });
    </script>
</body>
</html>