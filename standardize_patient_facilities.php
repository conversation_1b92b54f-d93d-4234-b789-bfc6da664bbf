<?php
/**
 * Standardize Patient Facility Names
 * 
 * This script standardizes facility names in the patient records to match the format
 * used in the users table, ensuring consistent access control.
 */

session_start();
include 'config.php';

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Store output in a buffer to display nicely at the end
ob_start();
$output = [];

// Check if running from command line
$is_cli = (php_sapi_name() === 'cli');

// Only proceed if running from CLI or logged in as admin/super_admin
if(!$is_cli) {
    if(!isset($_SESSION['user_id'])) {
        header("Location: login.php");
        exit();
    } else {
        $user_id = $_SESSION['user_id'];
        $check_admin = "SELECT role FROM users WHERE id = ?";
        $stmt = $conn->prepare($check_admin);
        $stmt->bind_param("i", $user_id);
        $stmt->execute();
        $result = $stmt->get_result();
        $current_user = $result->fetch_assoc();
        
        if($current_user['role'] !== 'admin' && $current_user['role'] !== 'super_admin') {
            echo "Access denied. Only admins can run this script.";
            exit();
        }
    }
}

$output[] = "Starting patient facility name standardization...";

// Get standard facility names from users table
$sql = "SELECT DISTINCT barangay FROM users ORDER BY barangay";
$result = $conn->query($sql);

$standard_facilities = [];
if ($result->num_rows > 0) {
    while($row = $result->fetch_assoc()) {
        $standard_facilities[] = $row['barangay'];
    }
}

// Get all patient facility names
$sql = "SELECT DISTINCT barangay FROM patients ORDER BY barangay";
$result = $conn->query($sql);

$patient_facilities = [];
if ($result->num_rows > 0) {
    while($row = $result->fetch_assoc()) {
        $patient_facilities[] = $row['barangay'];
    }
}

$output[] = "Found " . count($standard_facilities) . " standard facility names";
$output[] = "Found " . count($patient_facilities) . " different facility names in patient records";

// Function to get base name (without "Health Center")
function getBaseName($facility) {
    return trim(str_replace(["Health Center", "HEALTH CENTER", "health center"], "", $facility));
}

// Function to find the closest standard facility name
function findClosestFacility($patient_facility, $standard_facilities) {
    $patient_base = getBaseName($patient_facility);
    $best_match = null;
    $best_score = 0;
    
    foreach ($standard_facilities as $standard) {
        $standard_base = getBaseName($standard);
        
        // Check for exact base name match (case insensitive)
        if (strtolower($patient_base) === strtolower($standard_base)) {
            return $standard; // Perfect match found
        }
        
        // Otherwise, calculate similarity score
        similar_text(strtolower($patient_base), strtolower($standard_base), $score);
        if ($score > $best_score) {
            $best_score = $score;
            $best_match = $standard;
        }
    }
    
    // Only return matches above a certain threshold
    return ($best_score > 70) ? $best_match : null;
}

// For each patient facility name, find the closest standard name and update
$update_count = 0;
$no_match_count = 0;
$no_match_facilities = [];
$updates = [];

foreach ($patient_facilities as $patient_facility) {
    // Skip if the patient facility name already matches a standard name
    if (in_array($patient_facility, $standard_facilities)) {
        $output[] = "Facility name '$patient_facility' already matches standard format";
        continue;
    }
    
    $standard_name = findClosestFacility($patient_facility, $standard_facilities);
    
    if ($standard_name) {
        // Get count of affected records
        $count_sql = "SELECT COUNT(*) as count FROM patients WHERE barangay = ?";
        $stmt = $conn->prepare($count_sql);
        $stmt->bind_param("s", $patient_facility);
        $stmt->execute();
        $result = $stmt->get_result();
        $count_data = $result->fetch_assoc();
        $affected_count = $count_data['count'];
        
        // Update patient records
        $update_sql = "UPDATE patients SET barangay = ? WHERE barangay = ?";
        $stmt = $conn->prepare($update_sql);
        $stmt->bind_param("ss", $standard_name, $patient_facility);
        
        if ($stmt->execute()) {
            $update_count += $affected_count;
            $updates[] = [
                'from' => $patient_facility,
                'to' => $standard_name,
                'count' => $affected_count
            ];
            $output[] = "Updated '$patient_facility' to '$standard_name' (" . $affected_count . " patient records)";
        } else {
            $output[] = "Error updating facility name: " . $stmt->error;
        }
    } else {
        $no_match_count++;
        $no_match_facilities[] = $patient_facility;
        $output[] = "WARNING: No matching standard name found for '$patient_facility'";
    }
}

$output[] = "\n=== Standardization Summary ===";
$output[] = "Total patient records updated: $update_count";
$output[] = "Facility names without matches: $no_match_count";

if ($no_match_count > 0) {
    $output[] = "The following facility names could not be matched to a standard name:";
    foreach ($no_match_facilities as $facility) {
        $output[] = "- $facility";
    }
    $output[] = "These may need manual adjustment.";
}

$output[] = "Facility name standardization complete.";

// Get the output for CLI or prepare for HTML display
$log_content = implode("\n", $output);
if ($is_cli) {
    echo $log_content;
} else {
    // Display HTML output
    ?>
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Standardize Patient Facility Names</title>
        <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600&display=swap" rel="stylesheet">
        <style>
            body {
                font-family: 'Poppins', sans-serif;
                padding: 20px;
                max-width: 1200px;
                margin: 0 auto;
                line-height: 1.6;
            }
            .section {
                margin-bottom: 30px;
                padding: 20px;
                border-radius: 5px;
                background-color: #f8f9fa;
            }
            h1 {
                color: #2e7d32;
            }
            h2 {
                margin-top: 0;
                color: #2e7d32;
            }
            .log {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 5px;
                padding: 15px;
                font-family: monospace;
                white-space: pre-wrap;
                margin: 20px 0;
            }
            .warning {
                color: #856404;
                background-color: #fff3cd;
                padding: 10px;
                border-radius: 4px;
                margin-bottom: 10px;
            }
            table {
                width: 100%;
                border-collapse: collapse;
                margin-top: 20px;
            }
            th, td {
                padding: 12px 15px;
                text-align: left;
                border-bottom: 1px solid #ddd;
            }
            th {
                background-color: #e8f5e9;
            }
            tr:hover {
                background-color: #f5f5f5;
            }
            .success {
                background-color: #d4edda;
                color: #155724;
                padding: 10px;
                border-radius: 4px;
                margin-bottom: 10px;
            }
            .back-btn {
                display: inline-block;
                padding: 6px 12px;
                background: #607d8b;
                color: white;
                text-decoration: none;
                border-radius: 4px;
                font-size: 14px;
                margin-top: 20px;
            }
            .debug-btn {
                display: inline-block;
                padding: 6px 12px;
                background: #ff9800;
                color: white;
                text-decoration: none;
                border-radius: 4px;
                font-size: 14px;
                margin-top: 20px;
                margin-left: 10px;
            }
        </style>
    </head>
    <body>
        <h1>Standardize Patient Facility Names</h1>
        
        <?php if ($update_count > 0): ?>
        <div class="success">
            <strong>Success!</strong> Updated facility names for <?php echo $update_count; ?> patient records.
        </div>
        <?php endif; ?>
        
        <?php if ($no_match_count > 0): ?>
        <div class="warning">
            <strong>Warning:</strong> Could not find matching standard names for <?php echo $no_match_count; ?> facility names.
        </div>
        <?php endif; ?>
        
        <?php if (!empty($updates)): ?>
        <div class="section">
            <h2>Standardized Facility Names</h2>
            <table>
                <tr>
                    <th>Original Name</th>
                    <th>Standardized Name</th>
                    <th>Patient Records</th>
                </tr>
                <?php foreach ($updates as $update): ?>
                <tr>
                    <td><?php echo htmlspecialchars($update['from']); ?></td>
                    <td><?php echo htmlspecialchars($update['to']); ?></td>
                    <td><?php echo $update['count']; ?></td>
                </tr>
                <?php endforeach; ?>
            </table>
        </div>
        <?php endif; ?>
        
        <div class="section">
            <h2>Standardization Log</h2>
            <div class="log"><?php echo htmlspecialchars($log_content); ?></div>
        </div>
        
        <a href="dashboard.php" class="back-btn">Back to Dashboard</a>
        <a href="facility_debug.php" class="debug-btn">Debug Facility Names</a>
    </body>
    </html>
    <?php
}
?> 