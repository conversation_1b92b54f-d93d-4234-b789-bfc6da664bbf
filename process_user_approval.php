<?php
session_start();
include 'config.php';

// Check if user is logged in and is super admin
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'super_admin') {
    header("Location: login.php");
    exit();
}

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && isset($_POST['user_id'])) {
    $action = $_POST['action'];
    $user_id = $_POST['user_id'];
    
    if ($action === 'approve') {
        // Update the user status to active
        $sql = "UPDATE users SET status = 'active' WHERE id = ? AND status = 'pending'";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("i", $user_id);
        $stmt->execute();
        
        if ($stmt->affected_rows > 0) {
            $_SESSION['success'] = "User approved successfully!";
        } else {
            $_SESSION['error'] = "Failed to approve user. User may not be in pending status.";
        }
    } elseif ($action === 'reject') {
        // Get user details before deleting
        $get_user_sql = "SELECT username, fullname, barangay, designation FROM users WHERE id = ? AND status = 'pending'";
        $get_stmt = $conn->prepare($get_user_sql);
        $get_stmt->bind_param("i", $user_id);
        $get_stmt->execute();
        $user_result = $get_stmt->get_result();
        
        if ($user_result->num_rows > 0) {
            $user_data = $user_result->fetch_assoc();
            $username = $user_data['username'];
            $fullname = $user_data['fullname'];
            $barangay = $user_data['barangay'];
            $designation = $user_data['designation'];
            $rejected_by = $_SESSION['user_id'];
            
            // Insert into rejected_users table
            $insert_sql = "INSERT INTO rejected_users (username, fullname, barangay, designation, rejected_by) 
                          VALUES (?, ?, ?, ?, ?)";
            $insert_stmt = $conn->prepare($insert_sql);
            $insert_stmt->bind_param("ssssi", $username, $fullname, $barangay, $designation, $rejected_by);
            $insert_success = $insert_stmt->execute();
            
            // Delete the user from users table
            $delete_sql = "DELETE FROM users WHERE id = ? AND status = 'pending'";
            $delete_stmt = $conn->prepare($delete_sql);
            $delete_stmt->bind_param("i", $user_id);
            $delete_stmt->execute();
            
            if ($delete_stmt->affected_rows > 0 && $insert_success) {
                $_SESSION['success'] = "User rejected and added to rejected users list.";
            } else {
                $_SESSION['error'] = "Failed to reject user completely. Please check rejected users list.";
            }
        } else {
            $_SESSION['error'] = "User not found or not in pending status.";
        }
        
        // Close the statement
        $get_stmt->close();
    }
}

header("Location: manage_users.php");
exit();
?> 