<?php
// Enable error reporting
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Include database connection
include 'config.php';

echo "<h2>Users Table Structure</h2>";
$result = $conn->query("DESCRIBE users");
echo "<table border='1'>";
echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
while ($row = $result->fetch_assoc()) {
    echo "<tr>";
    echo "<td>" . $row['Field'] . "</td>";
    echo "<td>" . $row['Type'] . "</td>";
    echo "<td>" . $row['Null'] . "</td>";
    echo "<td>" . $row['Key'] . "</td>";
    echo "<td>" . $row['Default'] . "</td>";
    echo "<td>" . $row['Extra'] . "</td>";
    echo "</tr>";
}
echo "</table>";

echo "<h2>All Users</h2>";
$result = $conn->query("SELECT id, username, fullname, designation, barangay, role, status, created_at FROM users");
echo "<table border='1'>";
echo "<tr><th>ID</th><th>Username</th><th>Full Name</th><th>Designation</th><th>Barangay</th><th>Role</th><th>Status</th><th>Created At</th></tr>";
while ($row = $result->fetch_assoc()) {
    echo "<tr>";
    echo "<td>" . $row['id'] . "</td>";
    echo "<td>" . $row['username'] . "</td>";
    echo "<td>" . $row['fullname'] . "</td>";
    echo "<td>" . $row['designation'] . "</td>";
    echo "<td>" . $row['barangay'] . "</td>";
    echo "<td>" . $row['role'] . "</td>";
    echo "<td>" . $row['status'] . "</td>";
    echo "<td>" . $row['created_at'] . "</td>";
    echo "</tr>";
}
echo "</table>";

echo "<h2>Pending Users</h2>";
$result = $conn->query("SELECT id, username, fullname, designation, barangay, role, status, created_at FROM users WHERE status = 'pending'");
echo "<table border='1'>";
echo "<tr><th>ID</th><th>Username</th><th>Full Name</th><th>Designation</th><th>Barangay</th><th>Role</th><th>Status</th><th>Created At</th></tr>";
if ($result->num_rows > 0) {
    while ($row = $result->fetch_assoc()) {
        echo "<tr>";
        echo "<td>" . $row['id'] . "</td>";
        echo "<td>" . $row['username'] . "</td>";
        echo "<td>" . $row['fullname'] . "</td>";
        echo "<td>" . $row['designation'] . "</td>";
        echo "<td>" . $row['barangay'] . "</td>";
        echo "<td>" . $row['role'] . "</td>";
        echo "<td>" . $row['status'] . "</td>";
        echo "<td>" . $row['created_at'] . "</td>";
        echo "</tr>";
    }
} else {
    echo "<tr><td colspan='8'>No pending users found</td></tr>";
}
echo "</table>";
?> 