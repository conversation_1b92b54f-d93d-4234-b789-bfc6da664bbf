<?php
function logActivity($conn, $user_id, $action, $details = '') {
    $sql = "INSERT INTO user_activity (user_id, action, details) VALUES (?, ?, ?)";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("iss", $user_id, $action, $details);
    return $stmt->execute();
}

// Function to get user's recent activities
function getUserActivities($conn, $user_id, $limit = 50) {
    $sql = "SELECT * FROM user_activity WHERE user_id = ? ORDER BY created_at DESC LIMIT ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("ii", $user_id, $limit);
    $stmt->execute();
    return $stmt->get_result();
}
?> 