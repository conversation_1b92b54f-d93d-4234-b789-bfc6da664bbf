<?php
session_start();
include 'config.php';
include 'activity_logger.php';

// Check if user is logged in
if(!isset($_SESSION['user_id'])) {
    header("Location: login.php");
    exit();
}

// Get current user information
$user_id = $_SESSION['user_id'];
$check_user = "SELECT * FROM users WHERE id = ?";
$stmt = $conn->prepare($check_user);
$stmt->bind_param("i", $user_id);
$stmt->execute();
$result = $stmt->get_result();
$current_user = $result->fetch_assoc();

// Check if user is admin or super_admin
if($current_user['role'] !== 'admin' && $current_user['role'] !== 'super_admin' && $current_user['role'] !== 'staff') {
    header("Location: dashboard.php");
    exit();
}

$username = $_SESSION['username'];

// Get patient ID from URL
$patient_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

// Get patient information
if ($patient_id > 0) {
    $sql = "SELECT * FROM patients WHERE id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("i", $patient_id);
    $stmt->execute();
    $result = $stmt->get_result();
    $patient = $result->fetch_assoc();

    if (!$patient) {
        header("Location: view_patients.php");
        exit();
    }
}

// Handle form submission
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    try {
        $patient_id = $conn->real_escape_string($_POST['patient_id']);
        $medications = $conn->real_escape_string($_POST['medications']);
        $dosage = $conn->real_escape_string($_POST['dosage']);
        $instructions = $conn->real_escape_string($_POST['instructions']);
        $prescription_date = !empty($_POST['prescription_date']) ? $conn->real_escape_string($_POST['prescription_date']) : date('Y-m-d');

        // Check if the prescriptions table exists
        $table_exists = $conn->query("SHOW TABLES LIKE 'prescriptions'");

        if($table_exists->num_rows == 0) {
            // Create prescriptions table if it doesn't exist
            $create_table = "CREATE TABLE prescriptions (
                id INT(11) NOT NULL AUTO_INCREMENT,
                patient_id INT(11) NOT NULL,
                medications TEXT NOT NULL,
                dosage TEXT NOT NULL,
                instructions TEXT NOT NULL,
                prescription_date DATE NOT NULL,
                created_by INT(11) NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                PRIMARY KEY (id),
                KEY patient_id (patient_id)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";

            $conn->query($create_table);
        }

        // Insert prescription data
        $sql = "INSERT INTO prescriptions (patient_id, medications, dosage, instructions, prescription_date, created_by)
                VALUES (?, ?, ?, ?, ?, ?)";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("issssi", $patient_id, $medications, $dosage, $instructions, $prescription_date, $user_id);

        if ($stmt->execute()) {
            $prescription_id = $conn->insert_id;
            // Get patient name for the activity log
            $get_patient = "SELECT fullname FROM patients WHERE id = ?";
            $stmt_patient = $conn->prepare($get_patient);
            $stmt_patient->bind_param("i", $patient_id);
            $stmt_patient->execute();
            $result_patient = $stmt_patient->get_result();
            $patient_data = $result_patient->fetch_assoc();

            $details = "Added prescription for patient: " . $patient_data['fullname'] . " (ID: " . $patient_id . ")";
            logActivity($conn, $_SESSION['user_id'], "Add Prescription", $details);

            $_SESSION['success'] = "Prescription added successfully!";
            header("Location: view_prescriptions.php?id=" . $patient_id);
            exit();
        } else {
            $error_message = "Error: " . $stmt->error;
        }

        $stmt->close();
    } catch (Exception $e) {
        $error_message = "Error: " . $e->getMessage();
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Add Prescription</title>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Poppins', sans-serif;
        }

        body {
            font-family: 'Poppins', sans-serif;
            background: linear-gradient(135deg, #e8f5e9 0%, #c8e6c9 100%);
            margin: 0;
            padding: 0;
            min-height: 100vh;
        }

        /* Topbar Styles */
        .topbar {
            background: white;
            padding: 12px 20px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            display: flex;
            justify-content: space-between;
            align-items: center;
            height: 64px;
        }

        .logo-section {
            display: flex;
            align-items: center;
            height: 100%;
        }

        .logo {
            color: #2e7d32;
            font-size: 20px;
            font-weight: 600;
            display: flex;
            align-items: center;
            text-decoration: none;
            height: 100%;
            padding-right: 20px;
            margin-right: 20px;
            border-right: 1px solid #f0f0f0;
        }

        .logo-image {
            height: 40px;
            width: auto;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 3px;
        }

        .logo-text {
            display: flex;
            flex-direction: column;
        }

        .logo-title {
            color: #2e7d32;
            font-size: 18px;
            font-weight: 600;
            line-height: 1.1;
        }

        .logo-subtitle {
            color: #47a84e;
            font-size: 12px;
            font-weight: 500;
        }

        .nav-menu {
            display: flex;
            align-items: center;
            height: 100%;
        }

        .nav-item {
            display: flex;
            align-items: center;
            padding: 0 15px;
            color: #666;
            text-decoration: none;
            border-radius: 6px;
            transition: all 0.2s ease;
            font-size: 14px;
            height: 40px;
        }

        .nav-item:hover {
            background: #f0f7f0;
            color: #2e7d32;
        }

        .nav-item.active {
            background: #e8f5e9;
            color: #2e7d32;
            font-weight: 500;
        }

        .nav-item i {
            margin-right: 8px;
            width: 16px;
            text-align: center;
            font-size: 14px;
        }

        /* Main Content Styles */
        .main-content {
            margin-top: 60px;
            padding: 20px;
            width: 100%;
            box-sizing: border-box;
        }

        .user-profile {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .user-profile span {
            padding: 6px 12px;
            background: white;
            border-radius: 4px;
            color: #2e7d32;
            font-size: 13px;
            font-weight: 500;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
        }

        /* Form Styles */
        .form-container {
            background: white;
            border-radius: 8px;
            padding: 25px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
            max-width: 800px;
            margin: 0 auto;
        }

        .form-title {
            font-size: 18px;
            color: #333;
            margin-bottom: 20px;
            font-weight: 500;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-label {
            display: block;
            margin-bottom: 5px;
            color: #555;
            font-size: 13px;
        }

        .form-control {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            transition: border-color 0.2s ease;
        }

        .form-control:focus {
            border-color: #2e7d32;
            outline: none;
        }

        .btn-submit {
            background: #2e7d32;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: background 0.2s ease;
        }

        .btn-submit:hover {
            background: #1b5e20;
        }

        .success-message {
            background-color: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }

        .error-message {
            background-color: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }

        .form-text {
            font-size: 12px;
            color: #ff6b01;
            margin-top: 5px;
            display: block;
            font-weight: 500;
            padding-left: 5px;
        }

        .patient-info {
            background-color: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
            border-left: 4px solid #2e7d32;
        }

        .patient-info p {
            margin: 5px 0;
            font-size: 14px;
            color: #444;
        }

        .patient-info strong {
            font-weight: 600;
            color: #333;
        }

        .back-btn {
            display: inline-block;
            margin-bottom: 20px;
            padding: 8px 16px;
            background: #f0f7f0;
            color: #2e7d32;
            border-radius: 4px;
            text-decoration: none;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.2s ease;
        }

        .back-btn i {
            margin-right: 5px;
        }

        .back-btn:hover {
            background: #e8f5e9;
        }
    </style>
</head>
<body>
    <!-- Topbar -->
    <div class="topbar">
        <div class="logo-section">

            <nav class="nav-menu">
                <a href="dashboard.php" class="nav-item">
                    <i class="fas fa-home"></i>
                    Dashboard
                </a>
                <?php if($current_user['role'] === 'super_admin' || $current_user['role'] === 'admin' || $current_user['role'] === 'staff'): ?>
                <a href="add_patient.php" class="nav-item">
                    <i class="fas fa-user-plus"></i>
                    Add Patient
                </a>
                <?php endif; ?>
                <a href="view_patients.php" class="nav-item">
                    <i class="fas fa-users"></i>
                    Patient Records
                </a>
                <?php if($current_user['role'] !== 'staff'): ?>
                <a href="manage_users.php" class="nav-item">
                    <i class="fas fa-users-cog"></i>
                    User Management
                </a>
                <?php endif; ?>
                <a href="logout.php" class="nav-item">
                    <i class="fas fa-sign-out-alt"></i>
                    Logout
                </a>
            </nav>
        </div>
        <div class="user-profile">
            <span><?php echo htmlspecialchars($username); ?></span>
        </div>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <div class="form-container">
            <a href="<?php echo isset($patient) ? 'view_prescriptions.php?id=' . $patient_id : 'view_patients.php'; ?>" class="back-btn">
                <i class="fas fa-arrow-left"></i> Back
            </a>

            <h2 class="form-title">Add Prescription</h2>

            <?php if(isset($success_message)): ?>
                <div class="success-message"><?php echo $success_message; ?></div>
            <?php endif; ?>

            <?php if(isset($error_message)): ?>
                <div class="error-message"><?php echo $error_message; ?></div>
            <?php endif; ?>

            <?php if(isset($patient)): ?>
            <div class="patient-info">
                <p><strong>Patient:</strong> <?php echo htmlspecialchars($patient['fullname']); ?></p>
                <p><strong>Age:</strong> <?php echo htmlspecialchars($patient['age']); ?> years</p>
                <p><strong>Sex:</strong> <?php echo htmlspecialchars($patient['sex']); ?></p>
                <p><strong>Address:</strong> <?php echo htmlspecialchars($patient['address']); ?></p>
                <p><strong>Facility/Barangay:</strong> <?php echo htmlspecialchars($patient['barangay']); ?></p>
            </div>
            <?php endif; ?>

            <form method="POST" action="">
                <?php if(!isset($patient)): ?>
                <div class="form-group">
                    <label for="patient_id" class="form-label">Select Patient</label>
                    <select id="patient_id" name="patient_id" required class="form-control">
                        <option value="">-- Select Patient --</option>
                        <?php
                        // Get all patients for dropdown
                        $patients_sql = "SELECT id, fullname FROM patients ORDER BY fullname ASC";
                        $patients_result = $conn->query($patients_sql);
                        while($patient_row = $patients_result->fetch_assoc()) {
                            echo '<option value="' . $patient_row['id'] . '">' . htmlspecialchars($patient_row['fullname']) . '</option>';
                        }
                        ?>
                    </select>
                </div>
                <?php else: ?>
                <input type="hidden" name="patient_id" value="<?php echo $patient_id; ?>">
                <?php endif; ?>

                <div class="form-group">
                    <label for="prescription_date" class="form-label">Prescription Date</label>
                    <input type="date" id="prescription_date" name="prescription_date" class="form-control" value="<?php echo date('Y-m-d'); ?>">
                </div>

                <div class="form-group">
                    <label for="medications" class="form-label">Medications</label>
                    <textarea id="medications" name="medications" required placeholder="Enter medications" class="form-control" rows="4"></textarea>
                    <small class="form-text">List all medications to be prescribed. One medication per line.</small>
                </div>

                <div class="form-group">
                    <label for="dosage" class="form-label">Dosage</label>
                    <textarea id="dosage" name="dosage" required placeholder="Enter dosage instructions" class="form-control" rows="4"></textarea>
                    <small class="form-text">Specify amount and frequency (e.g., 1 tablet twice daily).</small>
                </div>

                <div class="form-group">
                    <label for="instructions" class="form-label">Special Instructions</label>
                    <textarea id="instructions" name="instructions" placeholder="Enter any special instructions" class="form-control" rows="4"></textarea>
                    <small class="form-text">Include any additional instructions (e.g., take with food, avoid alcohol).</small>
                </div>

                <button type="submit" class="btn-submit">Save Prescription</button>
            </form>
        </div>
    </div>
</body>
</html>