<?php
session_start();
include 'config.php';

// Check if already logged in
if(isset($_SESSION['user_id'])) {
    header("Location: dashboard.php");
    exit();
}

// Check for error messages
$error = "";
if(isset($_SESSION['register_error'])) {
    $error = $_SESSION['register_error'];
    unset($_SESSION['register_error']);
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Register - Barangay Health Center</title>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="style.css">
    <style>
        body {
            background: linear-gradient(135deg, #e8f5e9 0%, #c8e6c9 100%);
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            font-family: 'Poppins', sans-serif;
            padding: 20px;
        }
        
        .register-container {
            display: flex;
            width: 900px;
            max-width: 100%;
            height: auto;
            min-height: 600px;
            background: white;
            border-radius: 16px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
        }
        
        .welcome-panel {
            width: 40%;
            background: linear-gradient(135deg, #2e7d32 0%, #388e3c 100%);
            color: white;
            padding: 40px 30px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: space-between;
            position: relative;
            overflow: hidden;
            text-align: center;
        }
        
        .welcome-image {
            margin-top: 30px;
            margin-bottom: 20px;
            z-index: 1;
            display: flex;
            justify-content: center;
            width: 100%;
        }
        
        .welcome-image img {
            width: 160px;
            height: 140px;
            object-fit: contain;
            filter: drop-shadow(0 5px 10px rgba(0, 0, 0, 0.2));
        }
        
        .system-intro {
            font-size: 14px;
            opacity: 0.9;
            margin-bottom: 30px;
            text-align: center;
            z-index: 1;
            line-height: 1.6;
            font-weight: 500;
        }
        
        .select-group {
            position: relative;
            margin-bottom: 20px;
        }
        
        .select-group select {
            width: 100%;
            padding: 12px 40px 12px 40px;
            border: none;
            border-bottom: 2px solid #e0e0e0;
            font-size: 14px;
            transition: all 0.3s ease;
            background: transparent;
            -webkit-appearance: none;
            -moz-appearance: none;
            appearance: none;
            cursor: pointer;
        }
        
        .select-group select:focus {
            outline: none;
            border-bottom: 2px solid transparent;
            border-image: linear-gradient(135deg, #2e7d32 0%, #388e3c 100%);
            border-image-slice: 1;
        }
        
        .select-group i {
            position: absolute;
            left: 12px;
            top: 50%;
            transform: translateY(-50%);
            background: linear-gradient(135deg, #2e7d32 0%, #388e3c 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }
        
        .select-group::after {
            content: '\f107';
            font-family: 'Font Awesome 5 Free';
            font-weight: 900;
            position: absolute;
            right: 12px;
            top: 50%;
            transform: translateY(-50%);
            color: #388e3c;
            pointer-events: none;
        }
        
        .register-panel {
            width: 60%;
            padding: 40px;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }
        
        .register-title {
            background: linear-gradient(135deg, #2e7d32 0%, #388e3c 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            font-size: 24px;
            font-weight: 600;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            background: linear-gradient(135deg, #2e7d32 0%, #388e3c 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            display: block;
            margin-bottom: 8px;
            font-size: 14px;
            font-weight: 500;
        }
        
        .input-group {
            position: relative;
        }
        
        .input-group input {
            width: 100%;
            padding: 12px 40px 12px 40px;
            border: none;
            border-bottom: 2px solid #e0e0e0;
            font-size: 14px;
            transition: all 0.3s ease;
            background: transparent;
        }
        
        .input-group input:focus {
            outline: none;
            border-bottom: 2px solid transparent;
            border-image: linear-gradient(135deg, #2e7d32 0%, #388e3c 100%);
            border-image-slice: 1;
        }
        
        .input-group i {
            position: absolute;
            left: 12px;
            top: 50%;
            transform: translateY(-50%);
            background: linear-gradient(135deg, #2e7d32 0%, #388e3c 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }
        
        .register-btn {
            background: linear-gradient(135deg, #2e7d32 0%, #388e3c 100%);
            color: white;
            border: none;
            border-radius: 30px;
            padding: 12px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            width: 100%;
            margin-top: 20px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 10px rgba(46, 125, 50, 0.3);
        }
        
        .register-btn:hover {
            background: linear-gradient(135deg, #388e3c 0%, #2e7d32 100%);
            box-shadow: 0 6px 15px rgba(46, 125, 50, 0.4);
            transform: translateY(-2px);
        }
        
        .register-footer {
            text-align: center;
            margin-top: 20px;
        }
        
        .login-link {
            color: #388e3c;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .login-link:hover {
            background: linear-gradient(135deg, #2e7d32 0%, #388e3c 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }
        
        .error {
            background: #ffebee;
            color: #e53935;
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 20px;
            font-size: 14px;
            text-align: center;
        }
        
        @media (max-width: 768px) {
            .register-container {
                flex-direction: column;
            }
            
            .welcome-panel, .register-panel {
                width: 100%;
            }
            
            .welcome-panel {
                padding: 30px 20px;
                height: 200px;
            }
        }
    </style>
</head>
<body>
    <div class="register-container">
        <div class="welcome-panel">
            <div class="welcome-image">
                <img src="images/paranaque/logo.png" alt="Parañaque City Logo" style="width: 200px; height: 180px;">
            </div>
            <div class="system-intro">CITY OF PARAÑAQUE<br>BARANGAY HEALTH CENTER SYSTEM</div>
        </div>
        
        <div class="register-panel">
            <h2 class="register-title">CREATE ACCOUNT</h2>
            
            <?php if(!empty($error)): ?>
            <div class="error">
                <?php echo htmlspecialchars($error); ?>
            </div>
            <?php endif; ?>
            
            <form action="process_register.php" method="post">
                <div class="form-group">
                    <label for="fullname">Full Name</label>
                    <div class="input-group">
                        <i class="fas fa-user"></i>
                        <input type="text" id="fullname" name="fullname" placeholder="Enter your full name" required>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="designation">Designation</label>
                    <div class="input-group">
                        <i class="fas fa-briefcase"></i>
                        <input type="text" id="designation" name="designation" placeholder="Enter your designation" required>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="username">Username</label>
                    <div class="input-group">
                        <i class="fas fa-user"></i>
                        <input type="text" id="username" name="username" placeholder="Choose a username" required>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="password">Password</label>
                    <div class="input-group">
                        <i class="fas fa-lock"></i>
                        <input type="password" id="password" name="password" placeholder="Create a password" required>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="confirm_password">Confirm Password</label>
                    <div class="input-group">
                        <i class="fas fa-lock"></i>
                        <input type="password" id="confirm_password" name="confirm_password" placeholder="Confirm your password" required>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="barangay">Select Facility</label>
                    <div class="select-group">
                        <i class="fas fa-hospital"></i>
                        <select id="barangay" name="barangay" required>
                            <option value="">Select your facility</option>
                            <option value="Baclaran Health Center">Baclaran Health Center</option>
                            <option value="BF Homes Health Center">BF Homes Health Center</option>
                            <option value="Don Bosco Health Center">Don Bosco Health Center</option>
                            <option value="Don Galo Health Center">Don Galo Health Center</option>
                            <option value="La Huerta Health Center">La Huerta Health Center</option>
                            <option value="Marcelo Green Health Center">Marcelo Green Health Center</option>
                            <option value="Merville Health Center">Merville Health Center</option>
                            <option value="Moonwalk Health Center">Moonwalk Health Center</option>
                            <option value="San Antonio Health Center">San Antonio Health Center</option>
                            <option value="San Dionisio Health Center">San Dionisio Health Center</option>
                            <option value="San Isidro Health Center">San Isidro Health Center</option>
                            <option value="San Martin De Porres Health Center">San Martin De Porres Health Center</option>
                            <option value="Santo Niño Health Center">Santo Niño Health Center</option>
                            <option value="Sun Valley Health Center">Sun Valley Health Center</option>
                            <option value="Tambo Health Center">Tambo Health Center</option>
                            <option value="Vitalez Health Center">Vitalez Health Center</option>
                        </select>
                    </div>
                </div>
                
                <button type="submit" class="register-btn">Register</button>
                
                <div class="register-footer">
                    <span>Already have an account? </span>
                    <a href="login.php" class="login-link">Login</a>
                </div>  
            </form>
        </div>
    </div>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.querySelector('form');
            form.addEventListener('submit', function(e) {
                const password = document.getElementById('password').value;
                const confirmPassword = document.getElementById('confirm_password').value;
                
                if (password !== confirmPassword) {
                    e.preventDefault();
                    alert('Passwords do not match!');
                }
            });
            
            // Add tooltip functionality for facility selection
            const facilitySelect = document.getElementById('barangay');
            const facilityGroup = facilitySelect.closest('.select-group');
            
            // Create tooltip element
            const tooltip = document.createElement('div');
            tooltip.className = 'facility-tooltip';
            tooltip.style.display = 'none';
            tooltip.style.position = 'absolute';
            tooltip.style.backgroundColor = '#fff3e0';
            tooltip.style.border = '1px solid #f57c00';
            tooltip.style.borderRadius = '4px';
            tooltip.style.padding = '8px 12px';
            tooltip.style.fontSize = '12px';
            tooltip.style.color = '#333';
            tooltip.style.zIndex = '100';
            tooltip.style.width = '250px';
            tooltip.style.boxShadow = '0 2px 5px rgba(0,0,0,0.1)';
            tooltip.innerHTML = '<i class="fas fa-info-circle" style="color: #f57c00; margin-right: 5px;"></i> Once approved, you will automatically gain access to all patient records registered at your selected facility.';
            
            // Add the tooltip to the DOM
            document.body.appendChild(tooltip);
            
            // Show tooltip on select focus or hover
            facilitySelect.addEventListener('focus', showTooltip);
            facilityGroup.addEventListener('mouseenter', showTooltip);
            
            // Hide tooltip when focus or hover is lost
            facilitySelect.addEventListener('blur', hideTooltip);
            facilityGroup.addEventListener('mouseleave', hideTooltip);
            
            function showTooltip() {
                const rect = facilityGroup.getBoundingClientRect();
                tooltip.style.top = (rect.bottom + window.scrollY + 5) + 'px';
                tooltip.style.left = rect.left + 'px';
                tooltip.style.display = 'block';
            }
            
            function hideTooltip() {
                tooltip.style.display = 'none';
            }
        });
    </script>
</body>
</html> 