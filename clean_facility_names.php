<?php
session_start();
include 'config.php';

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Check if running from command line
$is_cli = (php_sapi_name() === 'cli');

// Only proceed if running from CLI or logged in as admin
if(!$is_cli && (!isset($_SESSION['user_id']) || ($_SESSION['role'] !== 'super_admin' && $_SESSION['role'] !== 'admin'))) {
    echo "Access denied. Only administrators can run this script.";
    exit;
}

echo "Facility Name Cleanup Tool\n";
echo "========================\n\n";

// Define standard facility list
$standard_facilities = [
    "Baclaran Health Center", 
    "BF Homes Health Center", 
    "Don Bosco Health Center", 
    "Don Galo Health Center", 
    "La Huerta Health Center",
    "Marcelo Green Health Center", 
    "Merville Health Center", 
    "Moonwalk Health Center", 
    "San Antonio Health Center", 
    "San Dionisio Health Center", 
    "San Isidro Health Center", 
    "San Martin De Porres Health Center", 
    "Santo Niño Health Center", 
    "Sun Valley Health Center", 
    "Tambo Health Center", 
    "Vitalez Health Center"
];

// Step 1: Check for duplicate "HEALTH CENTER" or any words
echo "STEP 1: Checking for duplicate words in facility names...\n";
$sql = "SELECT id, username, barangay FROM users";
$result = $conn->query($sql);

if ($result->num_rows > 0) {
    $updated_count = 0;
    while($row = $result->fetch_assoc()) {
        $user_id = $row['id'];
        $username = $row['username'];
        $old_value = $row['barangay'];
        
        // Split facility name into words and remove duplicates while preserving order
        $words = explode(' ', $old_value);
        $unique_words = [];
        foreach ($words as $word) {
            // Only add the word if it's not already the last word added (prevents duplicates)
            if (empty($unique_words) || strtolower(end($unique_words)) !== strtolower($word)) {
                $unique_words[] = $word;
            }
        }
        $new_value = implode(' ', $unique_words);
        
        // If changes were made, update the database
        if ($new_value !== $old_value) {
            $update_sql = "UPDATE users SET barangay = ? WHERE id = ?";
            $stmt = $conn->prepare($update_sql);
            $stmt->bind_param("si", $new_value, $user_id);
            
            if ($stmt->execute()) {
                $updated_count++;
                echo "  Fixed duplicates: User '$username' - Changed from '$old_value' to '$new_value'\n";
            } else {
                echo "  Error fixing duplicates for user '$username': " . $stmt->error . "\n";
            }
        }
    }
    
    echo "  $updated_count users updated to remove duplicate words.\n";
} else {
    echo "  No users found in the database.\n";
}

// Step 2: Add "Health Center" to facility names that don't have it
echo "\nSTEP 2: Standardizing facility names to include 'Health Center'...\n";
$sql = "SELECT id, username, barangay FROM users WHERE barangay NOT LIKE '%Health Center%' AND barangay NOT LIKE '%HEALTH CENTER%'";
$result = $conn->query($sql);

$updated_count = 0;
if ($result->num_rows > 0) {
    while($row = $result->fetch_assoc()) {
        $user_id = $row['id'];
        $username = $row['username'];
        $old_value = $row['barangay'];
        $new_value = $old_value;
        $updated = false;
        
        // Try to match to a standard facility name
        $best_match = null;
        $best_match_score = 0;
        foreach ($standard_facilities as $facility) {
            // Simple check - does the facility name start with this barangay name?
            $base_name = str_replace(' Health Center', '', $facility);
            if (stripos($old_value, $base_name) !== false) {
                $similarity = similar_text(strtolower($old_value), strtolower($base_name), $percent);
                if ($percent > $best_match_score) {
                    $best_match_score = $percent;
                    $best_match = $facility;
                }
            }
        }
        
        if ($best_match && $best_match_score > 50) {
            $new_value = $best_match;
            $updated = true;
        } else {
            // Just append "Health Center" if no close match found
            $new_value = $old_value . " Health Center";
            $updated = true;
        }
        
        if ($updated) {
            $update_sql = "UPDATE users SET barangay = ? WHERE id = ?";
            $stmt = $conn->prepare($update_sql);
            $stmt->bind_param("si", $new_value, $user_id);
            
            if ($stmt->execute()) {
                $updated_count++;
                echo "  Standardized: User '$username' - Changed from '$old_value' to '$new_value'\n";
            } else {
                echo "  Error standardizing facility name for user '$username': " . $stmt->error . "\n";
            }
        }
    }
}
echo "  $updated_count users updated to standardize facility names.\n";

// Step 3: Check that all facility names now match a standard facility name
echo "\nSTEP 3: Verifying all facility names are standardized...\n";
$sql = "SELECT DISTINCT barangay FROM users";
$result = $conn->query($sql);

if ($result->num_rows > 0) {
    $non_standard = [];
    while($row = $result->fetch_assoc()) {
        $facility = $row['barangay'];
        $is_standard = false;
        
        foreach ($standard_facilities as $standard) {
            if (strtolower($facility) === strtolower($standard)) {
                $is_standard = true;
                break;
            }
        }
        
        if (!$is_standard) {
            $non_standard[] = $facility;
        }
    }
    
    if (empty($non_standard)) {
        echo "  All facility names are now standardized.\n";
    } else {
        echo "  Some facility names still need to be standardized:\n";
        foreach ($non_standard as $facility) {
            echo "  - $facility\n";
            
            // Suggest standardization
            $closest = null;
            $highest_similarity = 0;
            foreach ($standard_facilities as $standard) {
                $similarity = similar_text(strtolower($facility), strtolower($standard), $percent);
                if ($percent > $highest_similarity) {
                    $highest_similarity = $percent;
                    $closest = $standard;
                }
            }
            
            if ($closest && $highest_similarity > 50) {
                echo "    Suggestion: Change to \"$closest\" (similarity: " . number_format($highest_similarity, 1) . "%)\n";
            }
        }
    }
}

// Final results
echo "\nFinal Facility Distribution:\n";
$sql = "SELECT barangay, COUNT(*) as count FROM users GROUP BY barangay ORDER BY barangay";
$result = $conn->query($sql);

if ($result->num_rows > 0) {
    while($row = $result->fetch_assoc()) {
        echo "  " . $row['barangay'] . ": " . $row['count'] . " users\n";
    }
} else {
    echo "  No users found in the database.\n";
}

$conn->close();
echo "\nFacility name cleanup complete.\n";
?> 