/* Modern UI Design System */

:root {
  /* Primary Gradients */
  --primary-gradient: linear-gradient(135deg, #20B2AA 0%, #3498db 100%);
  --secondary-gradient: linear-gradient(135deg, #3498db 0%, #6c5ce7 100%);
  --accent-gradient: linear-gradient(135deg, #00b4d8 0%, #7209b7 100%);
  --success-gradient: linear-gradient(135deg, #21D4FD 0%, #2152FF 100%);
  --warning-gradient: linear-gradient(135deg, #FF9A8B 0%, #FF6A88 100%);
  --danger-gradient: linear-gradient(135deg, #ff758c 0%, #ff7eb3 100%);
  
  /* Card Gradients */
  --card-gradient: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  --card-hover-gradient: linear-gradient(135deg, #f8f9fa 0%, #f1f3f5 100%);
  
  /* Background Gradients */
  --bg-gradient: linear-gradient(135deg, #f5f7fb 0%, #e4e8f0 100%);
  --sidebar-gradient: linear-gradient(180deg, #f5f7fb 0%, #e4e8f0 100%);
  
  /* Button Gradients */
  --btn-primary-gradient: linear-gradient(135deg, #20B2AA 0%, #3498db 100%);
  --btn-secondary-gradient: linear-gradient(135deg, #3498db 0%, #6c5ce7 100%);
  --btn-accent-gradient: linear-gradient(135deg, #00b4d8 0%, #7209b7 100%);
  
  /* Text Colors */
  --text-primary: #333;
  --text-secondary: #666;
  --text-light: #fff;
  --text-muted: #999;
  
  /* Shadow Effects */
  --shadow-sm: 2px 2px 5px rgba(0,0,0,0.05), -2px -2px 5px rgba(255,255,255,0.8);
  --shadow-md: 5px 5px 15px rgba(0,0,0,0.05), -5px -5px 15px rgba(255,255,255,0.8);
  --shadow-lg: 8px 8px 20px rgba(0,0,0,0.1), -8px -8px 20px rgba(255,255,255,0.8);
  --shadow-inset: inset 2px 2px 5px rgba(0,0,0,0.05), inset -2px -2px 5px rgba(255,255,255,0.8);
  
  /* Border Radius */
  --radius-sm: 8px;
  --radius-md: 12px;
  --radius-lg: 16px;
  --radius-round: 50%;
  
  /* Spacing */
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;
  
  /* Font Sizes */
  --font-xs: 0.75rem;
  --font-sm: 0.875rem;
  --font-md: 1rem;
  --font-lg: 1.25rem;
  --font-xl: 1.5rem;
  --font-2xl: 2rem;
}

/* Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-family: 'Poppins', sans-serif;
}

body {
  background: var(--bg-gradient);
  display: flex;
  font-size: var(--font-sm);
  min-height: 100vh;
}

/* Sidebar Component */
.sidebar {
  width: 220px;
  background: var(--card-gradient);
  height: 100vh;
  padding: var(--spacing-md);
  position: fixed;
  box-shadow: var(--shadow-md);
  backdrop-filter: blur(10px);
  z-index: 100;
}

.logo {
  background: var(--primary-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  font-size: var(--font-lg);
  font-weight: 600;
  margin-bottom: var(--spacing-xl);
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.nav-item {
  display: flex;
  align-items: center;
  padding: var(--spacing-sm) var(--spacing-md);
  color: var(--text-secondary);
  text-decoration: none;
  border-radius: var(--radius-sm);
  margin-bottom: var(--spacing-xs);
  transition: all 0.3s ease;
  font-size: var(--font-sm);
  background: var(--card-gradient);
  box-shadow: var(--shadow-sm);
}

.nav-item:hover, .nav-item.active {
  background: var(--primary-gradient);
  color: var(--text-light);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.nav-item i {
  margin-right: var(--spacing-md);
  width: 20px;
  text-align: center;
}

/* Main Content Area */
.main-content {
  margin-left: 220px;
  padding: var(--spacing-md);
  width: calc(100% - 220px);
}

/* Header Component */
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-lg);
  background: var(--card-gradient);
  padding: var(--spacing-md);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-md);
}

.search-bar {
  display: flex;
  align-items: center;
  background: var(--card-gradient);
  border-radius: var(--radius-sm);
  padding: var(--spacing-xs) var(--spacing-md);
  width: 250px;
  box-shadow: var(--shadow-inset);
}

.search-bar input {
  border: none;
  background: none;
  outline: none;
  width: 100%;
  margin-left: var(--spacing-sm);
  font-size: var(--font-sm);
}

.user-profile {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.user-profile img {
  width: 35px;
  height: 35px;
  border-radius: var(--radius-round);
  object-fit: cover;
}

/* Card Components */
.card {
  background: var(--card-gradient);
  border-radius: var(--radius-md);
  padding: var(--spacing-md);
  box-shadow: var(--shadow-md);
  transition: all 0.3s ease;
}

.card:hover {
  transform: translateY(-3px);
  box-shadow: var(--shadow-lg);
}

/* Button Components */
.btn {
  padding: var(--spacing-sm) var(--spacing-lg);
  border-radius: var(--radius-sm);
  border: none;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-xs);
  font-size: var(--font-sm);
  box-shadow: var(--shadow-sm);
}

.btn-primary {
  background: var(--btn-primary-gradient);
  color: var(--text-light);
}

.btn-secondary {
  background: var(--btn-secondary-gradient);
  color: var(--text-light);
}

.btn-accent {
  background: var(--btn-accent-gradient);
  color: var(--text-light);
}

.btn-light {
  background: white;
  color: var(--text-primary);
}

.btn-outlined {
  background: transparent;
  border: 1px solid currentColor;
}

.btn:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

/* Form Elements */
.form-group {
  margin-bottom: var(--spacing-md);
}

.form-group label {
  display: block;
  margin-bottom: var(--spacing-xs);
  color: var(--text-secondary);
  font-weight: 500;
}

.form-control {
  width: 100%;
  padding: var(--spacing-sm);
  border-radius: var(--radius-sm);
  border: none;
  background: var(--card-gradient);
  box-shadow: var(--shadow-inset);
  font-size: var(--font-sm);
  transition: all 0.3s ease;
}

.form-control:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.3), var(--shadow-inset);
}

/* Table Styles */
.table-container {
  overflow-x: auto;
  background: var(--card-gradient);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-md);
  margin-bottom: var(--spacing-lg);
}

table {
  width: 100%;
  border-collapse: collapse;
}

th {
  background: var(--primary-gradient);
  color: var(--text-light);
  text-align: left;
  padding: var(--spacing-sm) var(--spacing-md);
  font-weight: 500;
}

td {
  padding: var(--spacing-sm) var(--spacing-md);
  border-top: 1px solid rgba(0,0,0,0.05);
}

tr:hover {
  background: rgba(0,0,0,0.02);
}

/* Badge Component */
.badge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 20px;
  font-size: var(--font-xs);
  font-weight: 500;
}

.badge-primary {
  background: var(--primary-gradient);
  color: var(--text-light);
}

.badge-success {
  background: var(--success-gradient);
  color: var(--text-light);
}

.badge-warning {
  background: var(--warning-gradient);
  color: var(--text-light);
}

.badge-danger {
  background: var(--danger-gradient);
  color: var(--text-light);
}

/* Action Buttons */
.action-btn {
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-sm);
  color: var(--text-light);
  text-decoration: none;
  font-size: var(--font-xs);
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  transition: all 0.3s ease;
}

.edit-btn {
  background: var(--primary-gradient);
}

.delete-btn {
  background: var(--danger-gradient);
}

.view-btn {
  background: var(--secondary-gradient);
}

.activity-btn {
  background: var(--accent-gradient);
}

.action-btn:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-sm);
}

/* Responsive Adjustments */
@media (max-width: 992px) {
  .sidebar {
    width: 200px;
  }
  
  .main-content {
    margin-left: 200px;
    width: calc(100% - 200px);
  }
}

@media (max-width: 768px) {
  .sidebar {
    width: 180px;
  }
  
  .main-content {
    margin-left: 180px;
    width: calc(100% - 180px);
    padding: var(--spacing-sm);
  }
  
  .welcome-banner {
    flex-direction: column;
    text-align: center;
    gap: var(--spacing-md);
  }
  
  .banner-buttons {
    width: 100%;
    justify-content: center;
  }
}

@media (max-width: 576px) {
  body {
    flex-direction: column;
  }
  
  .sidebar {
    width: 100%;
    height: auto;
    position: relative;
  }
  
  .main-content {
    margin-left: 0;
    width: 100%;
  }
  
  .logo {
    margin-bottom: var(--spacing-md);
  }
  
  nav {
    display: flex;
    overflow-x: auto;
    padding-bottom: var(--spacing-sm);
  }
  
  .nav-item {
    white-space: nowrap;
    margin-right: var(--spacing-sm);
    margin-bottom: 0;
  }
} 