<?php
session_start();
include 'config.php';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = $_POST['username'];
    $password = $_POST['password'];
    
    // Validate input
    if (empty($username) || empty($password)) {
        $_SESSION['error'] = "Please fill in all fields.";
        header("Location: login.php");
        exit();
    }
    
    // Check if user exists
    $sql = "SELECT * FROM users WHERE username = ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("s", $username);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows === 0) {
        // Check if user is in rejected_users table
        $check_rejected_sql = "SELECT COUNT(*) as count FROM rejected_users WHERE username = ?";
        $stmt_rejected = $conn->prepare($check_rejected_sql);
        $stmt_rejected->bind_param("s", $username);
        $stmt_rejected->execute();
        $result_rejected = $stmt_rejected->get_result();
        $rejected_count = $result_rejected->fetch_assoc()['count'];
        
        if ($rejected_count > 0) {
            // Only set the essential session variables for rejected users
            $_SESSION['error'] = "Your account has been rejected.";
            $_SESSION['rejected'] = true;
            $_SESSION['rejected_message'] = "Your registration has been denied by an administrator.";
            header("Location: login.php");
            exit();
        }
        
        $_SESSION['error'] = "Username not found!";
        header("Location: login.php");
        exit();
    }
    
    $user = $result->fetch_assoc();
    
    // Verify password
    if (password_verify($password, $user['password'])) {
        // Check if account is pending approval
        if ($user['status'] === 'pending') {
            $_SESSION['pending'] = true;
            $_SESSION['error'] = "Your account is pending administrator approval.";
            $_SESSION['pending_message'] = "Your registration is currently under review. You will receive access once an administrator approves your account. This process typically takes 24-48 hours.";
            header("Location: login.php");
            exit();
        }
        
        // Check if account is active
        if ($user['status'] !== 'active') {
            $_SESSION['error'] = "Your account is not active. Please contact an administrator.";
            header("Location: login.php");
            exit();
        }
        
        // Login successful
        $_SESSION['user_id'] = $user['id'];
        $_SESSION['username'] = $user['username'];
        $_SESSION['role'] = $user['role'];
        $_SESSION['barangay'] = $user['barangay'];
        
        // Log activity - using the correct column names from your database structure
        try {
            // First check what columns exist in the user_activity table
            $checkTable = $conn->query("SHOW COLUMNS FROM user_activity");
            $columns = [];
            while($column = $checkTable->fetch_assoc()) {
                $columns[] = $column['Field'];
            }
            
            // Check if the table has 'action' and 'details' columns
            if (in_array('action', $columns) && in_array('details', $columns)) {
                // Using the old structure with action and details
                $log_sql = "INSERT INTO user_activity (user_id, action, details) VALUES (?, 'login', 'User logged in')";
                $log_stmt = $conn->prepare($log_sql);
                $log_stmt->bind_param("i", $user['id']);
                $log_stmt->execute();
                $log_stmt->close();
            } 
            // If the table structure is different, just skip logging for now
        } catch (Exception $e) {
            // Just continue if logging fails, don't prevent login
        }
        
        header("Location: dashboard.php");
        exit();
    } else {
        $_SESSION['error'] = "Invalid password!";
        header("Location: login.php");
        exit();
    }
}

// If not POST request, redirect to login page
header("Location: login.php");
exit();
?> 