<?php
/**
 * Facility Access Handler
 * 
 * This file contains functions to manage access rights when a user's facility assignment changes.
 * It ensures that users automatically gain access to patient records registered under their new facility.
 */

/**
 * Handles access rights when a user's facility is changed
 * 
 * @param object $conn Database connection
 * @param int $user_id The ID of the user whose facility has changed
 * @param string $old_facility The previous facility name
 * @param string $new_facility The new facility name
 * @return bool True if successful, false otherwise
 */
function handleFacilityChange($conn, $user_id, $old_facility, $new_facility) {
    // First, check if the activity_log table exists, and create it if not
    $check_table_sql = "SHOW TABLES LIKE 'activity_log'";
    $result = $conn->query($check_table_sql);
    
    if ($result->num_rows == 0) {
        // Table doesn't exist, create it
        $create_table_sql = "CREATE TABLE IF NOT EXISTS activity_log (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            activity_type VARCHAR(50) NOT NULL,
            details TEXT,
            timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
        )";
        $conn->query($create_table_sql);
    }
    
    // Now log the access change event
    $log_sql = "INSERT INTO activity_log (user_id, activity_type, details, timestamp) 
                VALUES (?, 'Facility Access Change', ?, NOW())";
    
    try {
        $stmt = $conn->prepare($log_sql);
        
        // Get user information for logging
        $user_query = "SELECT username, fullname FROM users WHERE id = ?";
        $user_stmt = $conn->prepare($user_query);
        $user_stmt->bind_param("i", $user_id);
        $user_stmt->execute();
        $user_result = $user_stmt->get_result();
        $user_data = $user_result->fetch_assoc();
        
        $details = "User '" . $user_data['username'] . "' (" . $user_data['fullname'] . ") access changed from '$old_facility' to '$new_facility'";
        $stmt->bind_param("is", $user_id, $details);
        $stmt->execute();
        
        return true;
    } catch (Exception $e) {
        // If there's still an error, log to PHP error log but don't break the application
        error_log("Error in handleFacilityChange: " . $e->getMessage());
        
        // Try using the user_activity table as fallback
        try {
            $fallback_sql = "INSERT INTO user_activity (user_id, action, details) VALUES (?, 'Facility Access Change', ?)";
            $fallback_stmt = $conn->prepare($fallback_sql);
            $fallback_stmt->bind_param("is", $user_id, $details);
            $fallback_stmt->execute();
        } catch (Exception $e2) {
            error_log("Error in fallback activity logging: " . $e2->getMessage());
        }
        
        return false;
    }
}

/**
 * Checks if a user has access to a specific patient's records
 * 
 * @param object $conn Database connection
 * @param int $user_id The ID of the user
 * @param int $patient_id The ID of the patient
 * @return bool True if user has access, false otherwise
 */
function userHasAccessToPatient($conn, $user_id, $patient_id) {
    // Get user's facility
    $user_sql = "SELECT role, barangay FROM users WHERE id = ?";
    $user_stmt = $conn->prepare($user_sql);
    $user_stmt->bind_param("i", $user_id);
    $user_stmt->execute();
    $user_result = $user_stmt->get_result();
    $user_data = $user_result->fetch_assoc();
    
    // Super admins have access to all patients
    if ($user_data['role'] === 'super_admin') {
        return true;
    }
    
    // Get patient's facility
    $patient_sql = "SELECT barangay FROM patients WHERE id = ?";
    $patient_stmt = $conn->prepare($patient_sql);
    $patient_stmt->bind_param("i", $patient_id);
    $patient_stmt->execute();
    $patient_result = $patient_stmt->get_result();
    $patient_data = $patient_result->fetch_assoc();
    
    // Users can only access patients from their facility
    return ($user_data['barangay'] === $patient_data['barangay']);
}

/**
 * Counts patients associated with a specific facility
 * 
 * @param object $conn Database connection
 * @param string $facility The facility name
 * @return int Number of patients
 */
function countPatientsInFacility($conn, $facility) {
    // Check for patients with exact facility name match
    $sql = "SELECT COUNT(*) as count FROM patients WHERE barangay = ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("s", $facility);
    $stmt->execute();
    $result = $stmt->get_result();
    $data = $result->fetch_assoc();
    $count = $data['count'];
    
    // If no matches with exact name, try case-insensitive search
    if ($count == 0) {
        $sql = "SELECT COUNT(*) as count FROM patients WHERE LOWER(barangay) = LOWER(?)";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("s", $facility);
        $stmt->execute();
        $result = $stmt->get_result();
        $data = $result->fetch_assoc();
        $count = $data['count'];
        
        // If still no matches, try with partial name (without "Health Center" suffix)
        if ($count == 0) {
            $facility_base = str_replace(" Health Center", "", $facility);
            $sql = "SELECT COUNT(*) as count FROM patients WHERE barangay LIKE CONCAT('%', ?, '%')";
            $stmt = $conn->prepare($sql);
            $stmt->bind_param("s", $facility_base);
            $stmt->execute();
            $result = $stmt->get_result();
            $data = $result->fetch_assoc();
            $count = $data['count'];
        }
    }
    
    return $count;
}
?> 